using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Enchantments;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class HeavenlyDrillTest : ModelTest
{

    [Test]
    public async Task TestWithThreeEnergy()
    {
        await PlayerCmd.SetEnergy(3, GetPlayer());
        await Play<HeavenlyDrill>(GetEnemy());

        // 8 * 3 * 2
        Assert.That(GetEnemy(), Has.LostHp(48));
    }

    [Test]
    public async Task TestWithLessThanThreeEnergy()
    {
        await PlayerCmd.SetEnergy(2, GetPlayer());

        CardModel card = CreateCard<HeavenlyDrill>();
        await Play(card, GetEnemy());
        Assert.That(GetEnemy(), <PERSON>.LostHp(16));
    }
}
