using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class FlattenTest : ModelTest
{
    [Test]
    public async Task TestAfterSummons()
    {
        Player player = GetPlayer();
        await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), player, 10, null);
        await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), player, 10, null);
        await Play<Flatten>(GetEnemy()); // 5 + 2 + 2

        Assert.That(GetEnemy(), Has.LostHp(9));
    }

    [Test]
    public async Task TestCarriesOverBetweenTurns()
    {
        Player player = GetPlayer();
        await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), player, 10, null);
        await PassToNextPlayerTurn();
        await Play<Flatten>(GetEnemy());

        Assert.That(GetEnemy(), Has.LostHp(7));
    }

    [Test]
    public async Task TestNoDamageWithoutOsty()
    {
        await Play<Flatten>(GetEnemy());
        Assert.That(GetEnemy(), Has.LostHp(0));
    }
}
