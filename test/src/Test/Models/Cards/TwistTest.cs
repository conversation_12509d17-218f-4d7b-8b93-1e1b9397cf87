using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class TwistTest : ModelTest
{
    [Test]
    public async Task TestAddsSoulToDiscard()
    {
        await Play<Twist>(GetEnemy());

        Assert.That(GetPile(PileType.Discard), <PERSON>.Cards(typeof(Soul), typeof(Soul), typeof(Twist)));
        Assert.That(GetPile(PileType.Discard).Cards.OfType<Soul>(), Is.All.Not.Upgraded());
    }

    [Test]
    public async Task TestUpgradeAddsUpgradedSoulToDiscard()
    {
        await PlayUpgraded<Twist>(GetEnemy());

        Assert.That(GetPile(PileType.Discard), <PERSON><PERSON>s(typeof(Soul), typeof(Soul), typeof(Twist)));
        Assert.That(GetPile(PileType.Discard).Cards.OfType<Soul>(), Is.All.Upgraded());
    }
}
