using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ScrapeTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        Creature enemy = GetEnemy();
        await Play<Scrape>(enemy);

        Assert.That(enemy, Has.LostHp(7));
    }

    [Test]
    public async Task TestCardDraws()
    {
        Creature enemy = GetEnemy();

        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(MockSkill().MockEnergyCost(0), GetPile(PileType.Draw));
        }

        await Play<Scrape>(enemy);

        Assert.That(GetPile(PileType.Hand).Cards.Count, Is.EqualTo(4));
        Assert.That(GetPile(PileType.Discard).Cards.Count, Is.EqualTo(1));
    }

    [Test]
    public async Task TestBaseDrawWithMultipleCardCosts()
    {
        Creature enemy = GetEnemy();

        for (int i = 0; i < 3; i++)
        {
            await CardPileCmd.Add(MockSkill().MockEnergyCost(1), GetPile(PileType.Draw));
        }

        await CardPileCmd.Add(MockSkill().MockEnergyCost(0), GetPile(PileType.Draw));

        await Play<Scrape>(enemy);

        Assert.That(GetPile(PileType.Hand).Cards.Count, Is.EqualTo(1));
        Assert.That(GetPile(PileType.Discard).Cards.Count, Is.EqualTo(4));
    }

    [Test]
    public async Task TestUpgradedDrawWithMultipleCardCosts()
    {
        Creature enemy = GetEnemy();

        for (int i = 0; i < 3; i++)
        {
            await CardPileCmd.Add(MockSkill().MockEnergyCost(1), GetPile(PileType.Draw));
        }

        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(MockSkill().MockEnergyCost(0), GetPile(PileType.Draw));
        }

        await PlayUpgraded<Scrape>(enemy);

        Assert.That(GetPile(PileType.Hand).Cards.Count, Is.EqualTo(2));
        Assert.That(GetPile(PileType.Discard).Cards.Count, Is.EqualTo(4));
    }

    [Test]
    public async Task TestDiscardsXCostCards()
    {
        await CardPileCmd.Add(MockSkill().MockEnergyCostX(), GetPile(PileType.Draw));
        await Play<Scrape>(GetEnemy());

        Assert.That(GetPile(PileType.Discard), Has.Cards(typeof(MockSkillCard), typeof(Scrape)));
    }

    [Test]
    public async Task TestDiscardsCursesAndStatuses()
    {
        await CardPileCmd.Add(MockCurse(), GetPile(PileType.Draw));
        await CardPileCmd.Add(MockStatus(), GetPile(PileType.Draw));
        await Play<Scrape>(GetEnemy());

        Assert.That(GetPile(PileType.Discard), Has.Cards(typeof(MockCurseCard), typeof(MockStatusCard), typeof(Scrape)));
    }

    [Test]
    public async Task TestDiscardsTemporarilyNonZeroCostCards()
    {
        CardModel card = MockSkill().MockEnergyCost(0);
        await CardPileCmd.Add(card, GetPile(PileType.Draw));
        card.EnergyCost.SetThisCombat(1);

        await Play<Scrape>(GetEnemy());

        // Card would normally be kept because it costs 0, but its cost has been increased to 1 this combat,
        // so discard it.
        Assert.That(GetPile(PileType.Discard), Has.Cards(typeof(MockSkillCard), typeof(Scrape)));
    }

    [Test]
    public async Task TestKeepsCardsThatTemporarilyCostZeroDueToLocalEffects()
    {
        CardModel card = MockSkill().MockEnergyCost(2);
        await CardPileCmd.Add(card, GetPile(PileType.Draw));
        card.EnergyCost.SetThisCombat(0);

        await Play<Scrape>(GetEnemy());

        // Card would normally be discarded because it costs 2, but its cost has been decreased to 0 this combat,
        // so keep it.
        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(MockSkillCard)));
    }

    [Test]
    public async Task TestKeepsCardsThatTemporarilyCostZeroDueToGlobalEffects()
    {
        CardModel card = MockSkill().MockEnergyCost(2);
        await CardPileCmd.Add(card, GetPile(PileType.Draw));
        await PowerCmd.Apply<FreeSkill>(GetPlayer().Creature, 1, null, null);

        await Play<Scrape>(GetEnemy());

        // Card would normally be discarded because it costs 2, but its cost has been decreased to 0 this combat,
        // so keep it.
        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(MockSkillCard)));
    }

    [Test]
    public async Task TestDiscardsZeroEnergyCostCardsWithStarCosts()
    {
        CardModel card = MockSkill().MockEnergyCost(0).MockStarCost(1);
        await CardPileCmd.Add(card, GetPile(PileType.Draw));

        await Play<Scrape>(GetEnemy());

        // Card is discarded even though it costs 1 star, because it costs 0 energy.
        Assert.That(GetPile(PileType.Hand).Cards.Count, Is.EqualTo(0));
    }
}
