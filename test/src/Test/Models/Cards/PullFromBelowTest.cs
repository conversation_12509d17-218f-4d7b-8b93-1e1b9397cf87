using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class PullFromBelowTest : ModelTest
{
    [Test]
    public async Task TestDealsDamageOnEthereal()
    {
        await Play<PullFromBelow>(GetEnemy());
        await Play(MockSkill().MockKeyword(CardKeyword.Ethereal));

        Assert.That(GetEnemy(), Has.<PERSON>Hp(7));
    }

    [Test]
    public async Task TestDealsNoDamageForNonEtherealCard()
    {
        await Play<PullFromBelow>(GetEnemy());
        await Play(MockSkill());

        Assert.That(GetEnemy(), Has.LostHp(0));
    }

    [Test]
    public async Task TestStacking()
    {
        await Play<PullFromBelow>(GetEnemy());
        await Play<PullFromBelow>(GetEnemy());
        await Play(MockSkill().MockKeyword(CardKeyword.Ethereal));

        Assert.That(GetEnemy(), Has.LostHp(14));
    }

    [Test]
    public async Task TestThatItDoesntCountItselfIfItIsEthereal()
    {
        CardModel card = CreateCard<PullFromBelow>();
        CardCmd.ApplyKeyword(card, CardKeyword.Ethereal);
        await Play(card, GetEnemy());

        Assert.That(GetEnemy(), Has.LostHp(0));
    }

}
