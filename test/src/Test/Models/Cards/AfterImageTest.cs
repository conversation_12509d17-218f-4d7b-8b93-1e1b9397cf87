using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class AfterImageTest : ModelTest
{
    [Test]
    public async Task TestByItself()
    {
        await Play<AfterImage>();
        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestBaseAfterPlaying1Card()
    {
        await Play<AfterImage>();
        await Play<FlickFlack>();

        Assert.That(GetPlayer().Creature, Has.Block(1));
    }

    [Test]
    public async Task TestAfterPlaying2Cards()
    {
        await Play<AfterImage>();

        for (int i = 0; i < 2; i++)
        {
            await Play<FlickFlack>();
        }

        Assert.That(GetPlayer().Crea<PERSON>, Has.Block(2));
    }

    [Test]
    public async Task TestWhenPlayingSecondAfterImage()
    {
        for (int i = 0; i < 2; i++)
        {
            await Play<AfterImage>();
        }

        // First After Image power should proc when second After Image is played, but second shouldn't.
        Assert.That(GetPlayer().Creature, Has.Block(1));
    }

    [Test]
    public async Task TestWhenPlayingCardsAfterSecondAfterImage()
    {
        for (int i = 0; i < 2; i++)
        {
            await Play<AfterImage>();
        }

        for (int i = 0; i < 2; i++)
        {
            await Play<FlickFlack>();
        }

        // 0 from first After Image
        // 1 from second After Image
        // 2 from first FlickFlack
        // 2 from second FlickFlack
        Assert.That(GetPlayer().Creature, Has.Block(5));
    }

    [Test]
    public async Task TestTriggersAfterThorns()
    {
        Creature enemy = GetEnemy();
        await Play<AfterImage>();
        await PowerCmd.Apply<Thorns>(enemy, 1, null, null);

        await Play<StrikeIronclad>(enemy);

        Creature player = GetPlayer().Creature;
        Assert.That(player, Has.Block(1));
        Assert.That(player, Has.LostHp(1));
    }
}
