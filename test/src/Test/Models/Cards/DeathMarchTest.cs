using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class DeathMarchTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        await Play<DeathMarch>(GetEnemy());
        Assert.That(GetEnemy(), Has.LostHp(8));
    }

    [Test]
    public async Task TestDamageWithSoulPlayed()
    {
        await Play<Soul>();
        await Play<DeathMarch>(GetEnemy());
        Assert.That(GetEnemy(), Has.LostHp(24));
    }

    [Test]
    public async Task TestDoesNotCountNonSoul()
    {
        await Play<MockSkillCard>();
        await Play<DeathMarch>(GetEnemy());
        Assert.That(GetEnemy(), Has.LostHp(8));
    }

    [Test]
    public async Task TestdoesntCountSoulPlayedLastTurn()
    {
        await Play<Soul>();
        await PassToNextPlayerTurn();
        await Play<DeathMarch>(GetEnemy());
        Assert.That(GetEnemy(), Has.LostHp(8));
    }
}
