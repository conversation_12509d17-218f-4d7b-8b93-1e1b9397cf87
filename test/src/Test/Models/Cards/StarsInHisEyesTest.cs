using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class StarsInHisEyesTest : ModelTest
{
    [Test]
    public async Task TestByItself()
    {
        await PlayerCmd.GainStars(1, GetPlayer());
        await Play<StarsInHisEyes>();
        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestBaseAfterPlaying1Card()
    {
        await PlayerCmd.GainStars(1, GetPlayer());
        await Play<StarsInHisEyes>();
        await Play<FlickFlack>();

        Assert.That(GetPlayer().Creature, Has.Block(2));
    }

    [Test]
    public async Task TestAfterPlaying2Cards()
    {
        await PlayerCmd.GainStars(1, GetPlayer());
        await Play<StarsInHisEyes>();

        for (int i = 0; i < 2; i++)
        {
            await Play<FlickFlack>();
        }

        Assert.That(GetPlayer().Creature, Has.Block(4));
    }

    [Test]
    public async Task TestWhenPlayingSecondStarsInHisEyes()
    {
        for (int i = 0; i < 2; i++)
        {
            await PlayerCmd.GainStars(1, GetPlayer());
            await Play<StarsInHisEyes>();
        }

        // First Stars In His Eyes should proc when second  Stars In His Eyes  is played, but second shouldn't.
        Assert.That(GetPlayer().Creature, Has.Block(2));
    }
}
