using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class DirgeTest : ModelTest
{
    [Test]
    public async Task TestSummonWith0Energy()
    {
        await PlayerCmd.SetEnergy(0, GetPlayer());
        await Play<Dirge>();

        Assert.That(GetPlayer().IsOstyMissing, Is.True);
    }

    [Test]
    public async Task TestSummonWith1Energy()
    {
        await PlayerCmd.SetEnergy(1, GetPlayer());
        await Play<Dirge>();

        Assert.That(GetPlayer().Osty!.CurrentHp, Is.EqualTo(3));
    }

    [Test]
    public async Task TestSummonWith3Energy()
    {
        await PlayerCmd.SetEnergy(3, GetPlayer());
        await Play<Dirge>();

        Assert.That(GetPlayer().Osty!.CurrentHp, Is.EqualTo(9));
    }

    [Test]
    public async Task TestSoulsWith2Energy()
    {
        await PlayerCmd.SetEnergy(2, GetPlayer());
        await Play<Dirge>();

        Assert.That(GetPile(PileType.Draw), Has.Cards(typeof(Soul), typeof(Soul)));
    }

    [Test]
    public async Task TestUpgradeSoulsWith2Energy()
    {
        await PlayerCmd.SetEnergy(2, GetPlayer());
        await Play<Dirge>();

        Assert.That(GetPile(PileType.Draw), Has.Cards(typeof(Soul), typeof(Soul)));
        Assert.That(GetPile(PileType.Discard).Cards.OfType<Soul>(), Is.All.Upgraded());
    }
}
