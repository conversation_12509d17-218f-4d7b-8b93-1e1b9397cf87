using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class NoEscapeTest : ModelTest
{
    [Test]
    public async Task TestBaseDoom()
    {
        Creature enemy = GetEnemy();
        await Play<NoEscape>(GetEnemy());
        Assert.That(enemy, Has.PowerAmount<Doom>(10));
    }

    [Test]
    public async Task TestIfEnemyHasFifteenDoom()
    {
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<Doom>(enemy, 15, null, null);
        await Play<NoEscape>(GetEnemy());

        // 15 + 10 + extra 5
        Assert.That(enemy, Has.PowerAmount<Doom>(30));
    }

    [Test]
    public async Task TestIfEnemyHas20Doom()
    {
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<Doom>(enemy, 20, null, null);
        await Play<NoEscape>(GetEnemy());

        // 20 + 10 + extra 10
        Assert.That(enemy, Has.PowerAmount<Doom>(40));
    }
}
