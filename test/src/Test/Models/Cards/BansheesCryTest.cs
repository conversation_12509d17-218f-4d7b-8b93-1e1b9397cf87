using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class BansheesCryTest : ModelTest
{
    [Test]
    public async Task TestBaseCost()
    {
        await Play<BansheesCry>(GetEnemy());

        Assert.That(GetPlayer(), Has.SpentEnergy(6));
    }

    [Test]
    public async Task TestPlayEtherealCardsBeforeCreation()
    {
        await Play(MockSkill().MockKeyword(CardKeyword.Ethereal).MockEnergyCost(0));
        await Play(MockSkill().MockKeyword(CardKeyword.Ethereal).MockEnergyCost(0));

        await Play<BansheesCry>(GetEnemy());

        // 6 (Banshee's Cry default) - 2 (2 ethereal cards played) * 2
        Assert.That(GetPlayer(), Has.SpentEnergy(2));
    }

    [Test]
    public async Task TestPlayEtherealCardsAfterCreation()
    {
        CardModel card = CreateCard<BansheesCry>();
        await CardPileCmd.Add(card, PileType.Hand);

        await Play(MockSkill().MockKeyword(CardKeyword.Ethereal).MockEnergyCost(0));
        await Play(MockSkill().MockKeyword(CardKeyword.Ethereal).MockEnergyCost(0));

        await Play(card, GetEnemy());

        // 6 (Banshee's Cry default) - (2 ethereal cards played) * 2
        Assert.That(GetPlayer(), Has.SpentEnergy(2));
    }

    [Test]
    public async Task TestPlayNonEtherealCards()
    {
        await Play(MockPower().MockEnergyCost(0));
        await Play(MockAttack().MockEnergyCost(0), GetEnemy());

        await Play<BansheesCry>(GetEnemy());

        Assert.That(GetPlayer(), Has.SpentEnergy(6));
    }

    [Test]
    public async Task TestPlayEtherealCardsBetweenTurns()
    {
        await Play(MockSkill().MockKeyword(CardKeyword.Ethereal).MockEnergyCost(0));
        await Play(MockSkill().MockKeyword(CardKeyword.Ethereal).MockEnergyCost(0));

        await PassToNextPlayerTurn();

        await Play<BansheesCry>(GetEnemy());

        // 6 (Banshee's Cry default) - 2 (2 ethereal cards played, since cost reduction lasts for the whole combat) * 2
        Assert.That(GetPlayer(), Has.SpentEnergy(2));
    }

    [Test]
    public async Task TestPlayEtherealCardsBetweenCombats()
    {
        await CardPileCmd.Add(CreateCard<BansheesCry>(CardScope.Climb), PileType.Deck);
        await RestartCombat();

        await Play(MockSkill().MockKeyword(CardKeyword.Ethereal).MockEnergyCost(0));
        await Play(MockSkill().MockKeyword(CardKeyword.Ethereal).MockEnergyCost(0));
        await RestartCombat();

        await Play(GetPile(PileType.Hand).Cards.OfType<BansheesCry>().First(), GetEnemy());

        // Ethereal card plays should not carry over between combats
        Assert.That(GetPlayer(), Has.SpentEnergy(6));
    }

    [Test]
    public async Task TestWithTemporaryEthereal()
    {
        MockCardModel card = MockSkill().MockKeyword(CardKeyword.Ethereal).MockEnergyCost(0);
        await Play(card);
        await Play(card);
        card.RemoveKeyword(CardKeyword.Ethereal);

        await Play<BansheesCry>(GetEnemy());

        // Should still have cost discount even if the played cards lost Ethereal
        Assert.That(GetPlayer(), Has.SpentEnergy(2));
    }
}
