using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class HauntTest : ModelTest
{
    [Test]
    public async Task TestEnemyLosesHpWhenSoulIsPlayed()
    {
        Creature enemy = GetEnemy();

        await Play<Haunt>();
        await Play<Soul>();

        Assert.That(enemy, Has.LostHp(5));
    }
}
