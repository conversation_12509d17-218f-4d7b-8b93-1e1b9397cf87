using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class MayhemTest : ModelTest
{
    [Test]
    public async Task TestWithEmptyDrawAndDiscardPiles()
    {
        await Play<Mayhem>();
        Assert.That(GetEnemy(), Has.LostHp(0));
        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestWithNonTargetedCard()
    {
        await FillDrawPileWithDefend();
        CardPile drawPile = GetPile(PileType.Draw);
        await CardPileCmd.Add(CreateCard<DefendSilent>(), drawPile);
        await Play<Mayhem>();
        await PassToNextPlayerTurn();
        Assert.That(GetPlayer().Creature, Has.Block(5));
    }

    [Test]
    public async Task TestWithTargetedCard()
    {
        CardPile drawPile = GetPile(PileType.Draw);
        await CardPileCmd.Add(CreateCard<StrikeSilent>(), drawPile);
        await Play<Mayhem>();
        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.LostHp(6));
    }

    [Test]
    public async Task TestWithUnplayableCard()
    {
        CardPile drawPile = GetPile(PileType.Draw);
        await CardPileCmd.Add(CreateCard<Wound>(), drawPile);
        await FillDrawPileWithDefend();
        await Play<Mayhem>();
        await PassToNextPlayerTurn();

        Assert.That(GetPile(PileType.Discard), Has.Cards(typeof(Wound)));
    }

    [Test]
    public async Task TestWithExhaustCard()
    {
        CardPile drawPile = GetPile(PileType.Draw);
        await CardPileCmd.Add(CreateCard<Slimed>(), drawPile);
        await Play<Mayhem>();
        await PassToNextPlayerTurn();

        Assert.That(GetPile(PileType.Exhaust), Has.Cards(typeof(Slimed)));
    }

    [Test]
    public async Task TestWithXCostCard()
    {
        CardPile drawPile = GetPile(PileType.Draw);
        await CardPileCmd.Add(CreateCard<Whirlwind>(), drawPile);
        await PlayUpgraded<Mayhem>();
        await PassToNextPlayerTurn();

        // Mayhem should play X-cost cards at full energy without using energy.
        // See https://slay-the-spire.fandom.com/wiki/Mayhem for more.
        Assert.That(GetEnemy(), Has.LostHp(500)); // Note: Test character has 100 energy
        Assert.That(GetPlayer(), Has.SpentEnergy(0));
    }

    [Test]
    public async Task TestBaseCost()
    {
        await Play<Mayhem>();
        Assert.That(GetPlayer(), Has.SpentEnergy(2));
    }

    [Test]
    public async Task TestUpgradedCost()
    {
        await PlayUpgraded<Mayhem>();
        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }

    [Test]
    public async Task TestWithSlyCard()
    {
        await FillDrawPileWithReflex(6);
        await Play<Mayhem>();

        await PassToNextPlayerTurn();
        // Five cards drawn, the last reflex was discarded but not played
        Assert.That(GetPile(PileType.Hand), Has.Cards(typeof(Reflex), typeof(Reflex), typeof(Reflex), typeof(Reflex), typeof(Reflex)));
        Assert.That(GetPile(PileType.Discard), Has.Cards(typeof(Reflex)));
    }

    private async Task FillDrawPileWithDefend(int count = 5)
    {
        CardPile drawPile = GetPile(PileType.Draw);

        for (int i = 0; i < count; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), drawPile);
        }
    }

    private async Task FillDrawPileWithReflex(int count = 5)
    {
        CardPile drawPile = GetPile(PileType.Draw);

        for (int i = 0; i < count; i++)
        {
            await CardPileCmd.Add(CreateCard<Reflex>(), drawPile);
        }
    }
}
