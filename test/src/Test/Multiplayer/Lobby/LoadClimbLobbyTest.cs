using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Game.Lobby;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Lobby;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.Unlocks;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Lobby;

public class LoadClimbLobbyTest
{
    [Test]
    public async Task TestThatLobbyDisconnectsClientAfterHandshakeTimeout()
    {
        TestGameService hostNetService = new(1, NetGameType.Host);
        MockLoadClimbLobbyListener mockListener = new();
        SerializableClimb climb = new ()
        {
            Players = [new SerializablePlayer { NetId = 1 }, new SerializablePlayer { NetId = 1000 }]
        };

        LoadClimbLobby lobby = new(hostNetService, mockListener, climb)
        {
            HandshakeTimeout = 50
        };

        TestGameService clientNetService = new(1000, NetGameType.Client);
        hostNetService.Connect(clientNetService);

        NetErrorInfo? info = null;
        clientNetService.Disconnected += i => info = i;

        await Task.Delay(100);

        Assert.That(clientNetService.IsConnected, Is.False);
        Assert.That(info, Is.Not.Null);
        Assert.That(info!.Value.GetReason(), Is.EqualTo(NetError.HandshakeTimeout));
    }

    [Test]
    public async Task TestThatLobbyDoesNotDisconnectClientIfHandshakeReceived()
    {
        TestGameService hostNetService = new(1, NetGameType.Host);
        MockLoadClimbLobbyListener mockListener = new();
        SerializableClimb climb = new ()
        {
            Players = [new SerializablePlayer { NetId = 1 }, new SerializablePlayer { NetId = 1000 }]
        };

        LoadClimbLobby lobby = new(hostNetService, mockListener, climb)
        {
            HandshakeTimeout = 50
        };

        TestGameService clientNetService = new(1000, NetGameType.Client);
        hostNetService.Connect(clientNetService);

        NetErrorInfo? info = null;
        clientNetService.Disconnected += i => info = i;

        clientNetService.SendMessage(new ClientLoadJoinRequestMessage());

        await Task.Delay(100);

        Assert.That(clientNetService.IsConnected, Is.True);
        Assert.That(info, Is.Null);
        Assert.That(hostNetService.SentMessages.OfType<ClientLobbyJoinResponseMessage>().FirstOrDefault(), Is.Not.Null);
    }
}
