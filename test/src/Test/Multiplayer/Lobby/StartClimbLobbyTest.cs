using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Game.Lobby;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Lobby;
using MegaCrit.Sts2.Core.Unlocks;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Lobby;

public class StartClimbLobbyTest
{
    [Test]
    public async Task TestThatLobbyDisconnectsClientAfterHandshakeTimeout()
    {
        TestGameService hostNetService = new(1, NetGameType.Host);
        MockStartClimbLobbyListener mockListener = new();
        StartClimbLobby lobby = new(GameMode.Standard, hostNetService, mockListener, 4)
        {
            HandshakeTimeout = 50
        };

        TestGameService clientNetService = new(1000, NetGameType.Client);
        hostNetService.Connect(clientNetService);

        NetErrorInfo? info = null;
        clientNetService.Disconnected += i => info = i;

        await Task.Delay(100);

        Assert.That(clientNetService.IsConnected, Is.False);
        Assert.That(info, Is.Not.Null);
        Assert.That(info!.Value.GetReason(), Is.EqualTo(NetError.HandshakeTimeout));
    }

    [Test]
    public async Task TestThatLobbyDoesNotDisconnectClientIfHandshakeReceived()
    {
        TestGameService hostNetService = new(1, NetGameType.Host);
        MockStartClimbLobbyListener mockListener = new();
        StartClimbLobby lobby = new(GameMode.Standard, hostNetService, mockListener, 4)
        {
            HandshakeTimeout = 50
        };

        TestGameService clientNetService = new(1000, NetGameType.Client);
        hostNetService.Connect(clientNetService);

        NetErrorInfo? info = null;
        clientNetService.Disconnected += i => info = i;

        clientNetService.SendMessage(new ClientLobbyJoinRequestMessage
        {
            maxAscensionUnlocked = 0,
            unlockState = UnlockState.none.ToSerializable()
        });

        await Task.Delay(100);

        Assert.That(clientNetService.IsConnected, Is.True);
        Assert.That(info, Is.Null);
        Assert.That(hostNetService.SentMessages.OfType<ClientLobbyJoinResponseMessage>().FirstOrDefault(), Is.Not.Null);
    }
}
