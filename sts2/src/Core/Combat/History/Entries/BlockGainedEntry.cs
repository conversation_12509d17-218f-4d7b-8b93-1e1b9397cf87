using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Combat.History.Entries;

public class BlockGainedEntry : CombatHistoryEntry
{
    public int Amount { get; }
    public Creature Receiver => Actor;

    public ValueProp Props { get; }

    public override string Description
    {
        get
        {
            string receiverId = GetId(Receiver);
            return $"{receiverId} gained {Amount} block";
        }
    }

    public BlockGainedEntry(int amount, Creature receiver, int roundNumber, CombatSide currentSide, CombatHistory history, ValueProp props) :
        base(receiver, roundNumber, currentSide, history)
    {
        Amount = amount;
        Props = props;
    }

    private static string GetId(Creature creature)
    {
        return creature.IsPlayer ? creature.Player!.Character.Id.Entry : creature.Monster!.Id.Entry;
    }
}
