using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat.History;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Ftue;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Screens.Map;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Combat;

public class CombatManager
{
    public const int baseHandDrawCount = 5;

    public static CombatManager Instance { get; } = new();

    /// <summary>
    /// Fired after combat is set up.
    /// Note that this happens a little bit before combat actually begins.
    /// </summary>
    public event Action<CombatState>? CombatSetUp;

    /// <summary>
    /// Fired when combat ends.
    /// </summary>
    public event Action<CombatRoom>? CombatEnded;

    /// <summary>
    /// Fired when combat is won.
    /// </summary>
    public event Action<CombatRoom>? CombatWon;

    /// <summary>
    /// Fired whenever the arrangement of creatures in the combat changes. Specifically, when:
    /// * A creature is added.
    /// * A creature is removed.
    /// * A creature's position changes.
    /// </summary>
    public event Action<CombatState>? CreaturesChanged;

    /// <summary>
    /// Fired whenever a new turn starts.
    /// </summary>
    public event Action<CombatState>? TurnStarted;

    /// <summary>
    /// Fired whenever a turn ends.
    /// </summary>
    public event Action<CombatState>? TurnEnded;

    /// <summary>
    /// Fired whenever a player ends their turn. Remember that, in multiplayer, this is not the same as switching to the
    /// enemy's turn.
    /// </summary>
    public event Action<Player, bool>? PlayerEndedTurn;

    /// <summary>
    /// Fired whenever a player un-does the end of their turn.
    /// </summary>
    public event Action<Player>? PlayerUnendedTurn;

    /// <summary>
    /// Fired when all players have fully committed to ending turn and all player actions are done (including end of turn
    /// hooks like Well-Laid Plans), but before the player hand flush.
    /// </summary>
    public event Action<CombatState>? AboutToSwitchToEnemyTurn;

    /// <summary>
    /// Fired when the local player's actions become disabled or enabled.
    /// </summary>
    public event Action<CombatState>? PlayerActionsDisabledChanged;

    private readonly HashSet<Player> _playersReadyToEndTurn = [];
    private readonly HashSet<Player> _playersReadyToBeginEnemyTurn = [];

    private readonly List<Player> _playersTakingExtraTurn = [];

    private CombatState? _state;

    /// <summary>
    /// THIS IS TEMPORARY AND SHOULD ONLY BE USED IN TESTS
    /// </summary>
    /// <returns></returns>
    public CombatState? DebugOnlyGetState() => _state;

    public bool IsPaused { get; private set; }

    /// <summary>
    /// Set to true when the player should not be able to interact with their hand or any potions.
    /// </summary>
    private bool _playerActionsDisabled;

    public bool PlayerActionsDisabled
    {
        get => _playerActionsDisabled;
        private set
        {
            if (_playerActionsDisabled != value)
            {
                _playerActionsDisabled = value;
                PlayerActionsDisabledChanged?.Invoke(_state!);
            }
        }
    }

    /// <summary>
    /// The list of players in the current turn that are taking an extra turn.
    /// Normally empty; only non-empty if there are players that used extra-turn-taking effects like
    /// <see cref="PaelsEye"/>.
    /// </summary>
    public IReadOnlyList<Player> PlayersTakingExtraTurn => _playersTakingExtraTurn;

    /// <summary>
    /// Are we currently in the "play phase" of combat?
    /// This phase is during the player's turn, when they're allowed to manually play cards.
    /// It starts after hand draw, and ends before hand flush.
    /// Note: If we add more phases besides "play phase" and "not play phase", we should turn this into an enum.
    /// </summary>
    public bool IsPlayPhase { get; private set; }

    /// <summary>
    /// Set to true in the time between when all players are ready to begin the enemy turn and when the enemy turn begins.
    /// </summary>
    public bool EndingPlayerTurnPhaseTwo { get; private set; }

    /// <summary>
    /// Set to true in the time during phase one of the end of the player's turn.
    /// </summary>
    public bool EndingPlayerTurnPhaseOne { get; private set; }

    public CombatStateTracker StateTracker { get; }
    public CombatHistory History { get; }

    /// <summary>
    /// Is the combat currently in progress?
    /// True when the combat is done being initialized and has fully started.
    /// False when:
    /// * The combat is first being initialized.
    /// * The combat is ending (the last monster has been killed).
    /// * We're in a non-combat room.
    /// </summary>
    public bool IsInProgress { get; private set; }

    /// <summary>
    /// Is the combat about to end?
    /// True when combat is in progress but all the enemies are dead, and there is nothing stopping combat from ending
    /// (e.g. Phrog Parasite spawning in new enemies).
    /// False when
    /// * Combat is in progress and 1+ enemies are still alive.
    /// * Combat is not in progress.
    /// </summary>
    public bool IsAboutToEnd
    {
        get
        {
            // Combat has already ended.
            if (!IsInProgress) return false;

            // There are living enemies.
            if (_state != null && _state.Enemies.Any(e => e.IsAlive)) return false;

            // Something is stopping combat from ending (probably spawning new enemies).
            if (Hook.ShouldStopCombatFromEnding(_state!)) return false;

            return true;
        }
    }

    private CombatManager()
    {
        History = new CombatHistory();
        StateTracker = new CombatStateTracker(this);
    }

    public void SetUpCombat(CombatState state)
    {
        if (_state != null) throw new InvalidOperationException("Make sure to reset the combat before setting up a new one.");

        _state = state;

        _state.MultiplayerScalingModel?.OnCombatEntered(_state);
        StateTracker.SetState(state);
        _playersTakingExtraTurn.Clear();

        foreach (Player player in state.Players)
        {
            player.ResetCombatState();
        }

        // We split this into a separate loop from the ResetCombatState loop above, so that all players have a fresh
        // combat state before populating.
        foreach (Player player in state.Players)
        {
            player.PopulateCombatState(player.ClimbState.Rng.Shuffle, state);
        }

        NetCombatCardDb.Instance.StartCombat(state.Players);

        foreach (Creature creature in state.Creatures)
        {
            AddCreature(creature);
        }

        CombatSetUp?.Invoke(state);
    }

    public void AfterCombatRoomLoaded()
    {
        TaskHelper.RunSafely(StartCombatInternal());
    }

    public async Task StartCombatInternal()
    {
        if (_state!.Encounter!.HasBgm)
        {
            NClimbMusicController.Instance?.PlayCustomMusic(_state!.Encounter.CustomBgm);
        }

        // Run all initial creature logic
        foreach (Creature creature in _state!.Creatures)
        {
            await AfterCreatureAdded(creature);
        }

        // Pause the action queue so that multiplayer actions received don't begin until combat has been fully set up.
        ClimbManager.Instance.ActionExecutor.Pause();

        // Initialize the action queue executor for the beginning of combat, pausing the queue, so any queued actions
        // are sent only after the player turn begins.
        ClimbManager.Instance.ActionQueueSynchronizer.SetCombatState(ActionSynchronizerCombatState.NotPlayPhase);

        IsInProgress = true;
        await Hook.BeforeCombatStart(_state!.ClimbState, _state);

        NClimbMusicController.Instance?.UpdateTrack();

        // Don't show the combat banner if the Combat Basics ftue is going to show up
        if (SaveManager.Instance.SeenFtue(NCombatRulesFtue.id))
        {
            NCombatRoom.Instance?.AddChildSafely(NCombatStartBanner.Create());
        }

        await Cmd.CustomScaledWait(0.5f, 1f, 1.2f);

        await StartTurn();

        if (!SaveManager.Instance.SeenFtue(NCombatRulesFtue.id))
        {
            NModalContainer.Instance?.Add(NCombatRulesFtue.Create()!);
        }
    }

    private async Task StartTurn(Func<Task>? actionDuringEnemyTurn = null)
    {
        if (!IsInProgress) return;

        bool isExtraPlayerTurn = _playersTakingExtraTurn.Count > 0;
        List<Creature> creaturesStartingTurn;
        List<Player> playersStartingTurn;

        if (_state!.CurrentSide == CombatSide.Player && isExtraPlayerTurn)
        {
            // This is for multiplayer scenarios involving an extra-turn-taking effect.
            // Only the player that has this effect gets an extra turn.
            creaturesStartingTurn = _playersTakingExtraTurn.Select(p => p.Creature).ToList();
            playersStartingTurn = _playersTakingExtraTurn.ToList();
        }
        else
        {
            creaturesStartingTurn = _state!.CreaturesOnCurrentSide.ToList();
            playersStartingTurn = _state.CurrentSide == CombatSide.Player ? _state!.Players.ToList() : [];
        }

        foreach (Creature creature in creaturesStartingTurn)
        {
            creature.BeforeTurnStart(_state.RoundNumber, _state.CurrentSide);
        }

        await Hook.BeforeSideTurnStart(_state!, _state.CurrentSide);

        if (_state.CurrentSide == CombatSide.Player)
        {
            // Allow player hand to be interacted with.
            PlayerActionsDisabled = false;

            _playersReadyToEndTurn.Clear();
            _playersReadyToBeginEnemyTurn.Clear();

            if (_state.RoundNumber != 1)
            {
                NCombatRoom.Instance?.AddChildSafely(NPlayerTurnBanner.Create(_state.RoundNumber));
            }

            if (!isExtraPlayerTurn)
            {
                foreach (Creature creature in _state!.Enemies)
                {
                    creature.PrepareForNextTurn(_state.PlayerCreatures);
                }
            }
        }
        else
        {
            NCombatRoom.Instance?.AddChildSafely(NEnemyTurnBanner.Create()!);
        }

        await Cmd.CustomScaledWait(0.5f, 0.8f, 1.2f);

        // Block clear happens in here.
        foreach (Creature creature in creaturesStartingTurn)
        {
            await creature.AfterTurnStart(_state.RoundNumber, _state.CurrentSide);
        }

        // Call the AfterBlockCleared hook only after all creatures have cleared block. It used to be in Creature.
        // AfterTurnStart, but that caused Beacon of Hope to trigger, then for the other player who gained block to clear
        // the block gained.
        foreach (Creature creature in creaturesStartingTurn)
        {
            await Hook.AfterBlockCleared(_state, creature);
        }

        foreach (Player player in playersStartingTurn)
        {
            HookPlayerChoiceContext playerChoiceContext = new(player, LocalContext.NetId!.Value);
            Task task = SetupPlayerTurn(player, playerChoiceContext);
            await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);
        }

        await Hook.AfterSideTurnStart(_state!, _state.CurrentSide);

        if (_state.CurrentSide == CombatSide.Player)
        {
            foreach (Player player in playersStartingTurn)
            {
                HookPlayerChoiceContext playerChoiceContext = new(player, LocalContext.NetId!.Value);
                Task task = player.PlayerCombatState!.OrbQueue.AfterTurnStart(playerChoiceContext);
                await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);
            }

            ClimbManager.Instance.ChecksumTracker.GenerateChecksum("After player turn start", null);

            // Automatically set already-dead players and those who aren't part of this turn to ready
            foreach (Player player in _state.Players)
            {
                if (player.Creature.IsDead || !playersStartingTurn.Contains(player))
                {
                    SetReadyToEndTurn(player, false);
                }
            }

            // Check in case anything killed the enemies at the start of the player turn (i.e. Kingly Punch).
            await CheckWinCondition();

            // Do not execute the rest of this method if the above check ended combat.
            if (!IsInProgress) return;

            // Unpause the action executor, which is paused at the start of combat and at the end of the player turn.
            // Note: This is different from CombatManager.Unpause().
            ClimbManager.Instance.ActionExecutor.Unpause();

            // Set to player turn, allowing actions to flow again.
            ClimbManager.Instance.ActionQueueSynchronizer.SetCombatState(ActionSynchronizerCombatState.PlayPhase);
            IsPlayPhase = true;

            TurnStarted?.Invoke(_state);
        }
        else
        {
            TurnStarted?.Invoke(_state);

            ClimbManager.Instance.ChecksumTracker.GenerateChecksum("After enemy turn start", null);

            await WaitForUnpause();

            // Check in case anything killed the enemies at the start of turn (i.e. Poison).
            await CheckWinCondition();

            // Do not execute the rest of this method if the above check ended combat.
            if (!IsInProgress) return;

            await ExecuteEnemyTurn(actionDuringEnemyTurn);
        }
    }

    /// <summary>
    /// Executes the turn start sequence for a given player.
    /// If the player's turn start executes a player choice (e.g. Mayhem plays Cosmic Indifference), then the entire
    /// sequence is paused for this player. However, other players' turn start sequences may continue, and they may
    /// play cards while this is occuring.
    /// </summary>
    /// <param name="player">The player whose turn to setup.</param>
    /// <param name="playerChoiceContext">The player choice context to pass to hooks that take it.</param>
    private async Task SetupPlayerTurn(Player player, HookPlayerChoiceContext playerChoiceContext)
    {
        if (player.Creature.IsDead) return;

        if (Hook.ShouldPlayerResetEnergy(_state!, player))
        {
            player.PlayerCombatState!.ResetEnergy();
        }
        else
        {
            player.PlayerCombatState!.AddMaxEnergyToCurrent();
        }

        await Hook.AfterEnergyReset(_state!, player);
        await Hook.BeforeHandDraw(_state!, player, playerChoiceContext);

        decimal handDraw = Hook.ModifyHandDraw(_state!, player, baseHandDrawCount, out IEnumerable<AbstractModel> modifiers);
        await Hook.AfterModifyingHandDraw(_state!, modifiers);

        if (_state!.RoundNumber == 1)
        {
            CardPile drawPile = PileType.Draw.GetPile(player);

            // Find all the cards with an enchantment that forces them to the bottom and move them to the
            // bottom.
            List<CardModel> bottomCards = drawPile
                .Cards
                .Where(c => c.Enchantment is { ShouldStartAtBottomOfDrawPile: true })
                .ToList();

            foreach (CardModel bottomCard in bottomCards)
            {
                drawPile.MoveToBottomInternal(bottomCard);
            }

            // Find all the Innate cards (_without_ an enchantment that forces them to the bottom) and move them
            // to the top.
            List<CardModel> innateCards = drawPile
                .Cards
                .Where(c => c.Keywords.Contains(CardKeyword.Innate))
                .Except(bottomCards)
                .ToList();

            foreach (CardModel innateCard in innateCards)
            {
                drawPile.MoveToTopInternal(innateCard);
            }

            // If there are more innate cards than the normal hand draw size, allow extra cards to be drawn...
            handDraw = Math.Max(handDraw, innateCards.Count);

            // ...but not past the maximum hand size.
            handDraw = Math.Min(handDraw, CardPile.maxCardsInHand);
        }

        await CardPileCmd.Draw(playerChoiceContext, handDraw, player, true);

        await Hook.AfterPlayerTurnStart(_state!, playerChoiceContext, player);
    }

    /// <summary>
    /// Called in EndPlayerTurnAction to indicate that the player is ready to execute end-of-turn events.
    /// </summary>
    /// <param name="player">The player that readied up.</param>
    /// <param name="canBackOut">In multiplayer, notes if the player is allowed to back out of ending their turn.</param>
    public void SetReadyToEndTurn(Player player, bool canBackOut)
    {
        _playersReadyToEndTurn.Add(player);
        PlayerEndedTurn?.Invoke(player, canBackOut);

        if (AllPlayersReadyToEndTurn())
        {
            // We do not wish this to block the shared queue while
            // end of turn hooks are running, so we execute this as its own task
            Log.LogMessage(LogLevel.Debug, LogType.GameSync, "All players ready to end turn");
            TaskHelper.RunSafely(AfterAllPlayersReadyToEndTurn());
        }
    }

    public void UndoReadyToEndTurn(Player player)
    {
        _playersReadyToEndTurn.Remove(player);

        if (LocalContext.IsMe(player))
        {
            PlayerActionsDisabled = false;
        }

        PlayerUnendedTurn?.Invoke(player);
    }

    /// <summary>
    /// Call this when the end turn button is pressed to disable local player actions until the start of the next turn.
    /// In multiplayer, this prevents the player from playing cards after they have ended turn.
    /// In both SP and MP, this prevents the player from playing cards before the AfterTurnStart hook has run.
    /// It's important that we do this when the end turn button is pressed, instead of when the EndTurnAction is
    /// processed, because the player might try to execute actions while the end turn action is waiting in the queue.
    /// This is a little fragile; if actions do slip through in MP, it has the potential to cause a state divergence.
    /// Revisit if needed - we might need to discard actions on the host side (which ends up being way more complicated).
    /// </summary>
    public void OnEndedTurnLocally()
    {
        PlayerActionsDisabled = true;
    }

    /// <summary>
    /// Called in ReadyToBeginEnemyTurnAction to indicate that the player is ready to switch to the monster turn (or
    /// extra player turn, if necessary). Note that this is called automatically, and is not player-driven.
    /// </summary>
    /// <param name="player">The player that is ready to switch sides.</param>
    public void SetReadyToBeginEnemyTurn(Player player)
    {
        // If we receive this action after combat ends, do not execute it, otherwise we will re-pause the player queue
        // and map vote actions will not be executed.
        // Note that there is no condition under which this should currently happen - this is more of a failsafe.
        if (!IsInProgress) Log.Error("Trying to set player ready to begin enemy turn, but combat is over!");

        _playersReadyToBeginEnemyTurn.Add(player);

        if ((_playersReadyToBeginEnemyTurn.Count == _state!.Players.Count && _state.CurrentSide == CombatSide.Player) ||
            // This is a hack to allow simulating multiplayer in singleplayer by adding multiple players through
            // BootstrapSettings
            ClimbManager.Instance.NetService.Type == NetGameType.Singleplayer)
        {
            Log.LogMessage(LogLevel.Debug, LogType.GameSync, "All players ready to begin enemy turn, switching sides");
            TaskHelper.RunSafely(AfterAllPlayersReadyToBeginEnemyTurn());
        }
    }

    /// <returns>True if the passed player has hit the end turn button, and the next player turn has not yet begun.</returns>
    public bool IsPlayerReadyToEndTurn(Player player)
    {
        return _playersReadyToEndTurn.Contains(player);
    }

    public bool AllPlayersReadyToEndTurn()
    {
        bool allPlayersReady = _playersReadyToEndTurn.Count == _state!.Players.Count;

        // The IsSinglePlayer check is a hack to allow simulating multiplayer in singleplayer by adding multiple players
        // through BootstrapSettings and skipping the "everyone ready" check if that is the case.
        return ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer || (allPlayersReady && _state!.CurrentSide == CombatSide.Player);
    }

    private async Task EndEnemyTurn()
    {
        if (_state!.CurrentSide != CombatSide.Enemy)
        {
            throw new InvalidOperationException($"EndPlayerTurn called while the current side is {_state.CurrentSide}!");
        }

        await WaitForUnpause();
        await EndEnemyTurnInternal();

        // Check in case anything killed the enemies at the end of their turn (i.e. Doom).
        await CheckWinCondition();

        if (!IsAboutToEnd)
        {
            SwitchSides();

            await WaitForUnpause();
            await StartTurn();
        }
    }

    // TODO: Maybe make this a private method that runs in response to a CombatState event.
    public void AddCreature(Creature creature)
    {
        if (!_state!.ContainsCreature(creature))
        {
            throw new InvalidOperationException("CombatState must already contain creature.");
        }

        creature.Monster?.SetUpForCombat();

        if (creature.SlotName != null)
        {
            _state.SortEnemiesBySlotName();
        }

        StateTracker.Subscribe(creature);
        CreaturesChanged?.Invoke(_state);
    }

    /// <summary>
    /// Called after both the Creature has been added to the room _and_ the NCreature is spawned.
    /// </summary>
    /// <param name="creature"></param>
    public async Task AfterCreatureAdded(Creature creature)
    {
        await creature.AfterAddedToRoom();

        // If a monster is spawned during the player turn, roll its move immediately.
        // If it's spawned during the enemy turn, wait until the player's next turn to roll its move.
        if (creature.IsEnemy && _state!.CurrentSide == CombatSide.Player)
        {
            creature.Monster!.RollMove(_state.Players.Select(p => p.Creature));
        }
    }

    /// <summary>
    /// Check for the player's hand to be empty and run the appropriate hooks if it is.
    ///
    /// We can't just do this check every time the hand size changes, because sometimes we're in the middle of a
    /// sequence of effects and we want to wait to check until they're all done.
    ///
    /// For example, if we have <see cref="UnceasingTop"/> and the last card in our hand is <see cref="PommelStrike"/>
    /// and we play it, we have to wait to check hand size until Pommel Strike is done being played, otherwise we'll
    /// draw two cards (one when your hand becomes "empty" immediately after Pommel Strike moves to the Play pile, and
    /// another after Pommel Strike's draw command executes).
    ///
    /// So, instead of automatically doing this check every time the hand size changes, we manually check after a card
    /// is played, and after a potion is used, since these are the two ways a player can manually interact with combat
    /// state (besides ending turn, which should not trigger an empty hand check). If we ever add more ways, we should
    /// add this check in those too, and update this comment.
    /// </summary>
    /// <param name="choiceContext">Object that keeps context of the action this is called from.</param>
    /// <param name="player">Player whose hand we want to check.</param>
    public async Task CheckForEmptyHand(PlayerChoiceContext choiceContext, Player player)
    {
        if (!IsInProgress) return;
        if (PileType.Hand.GetPile(player).Cards.Any()) return;

        await Hook.AfterHandEmptied(_state!, choiceContext, player);
    }

    public void Reset()
    {
        if (_state != null)
        {
            foreach (Creature creature in _state.Creatures.ToList())
            {
                creature.Reset();
                RemoveCreature(creature);
                _state.RemoveCreature(creature);
            }

            _state = null;
        }

        IsInProgress = false;
        History.Clear();
    }

    public async Task HandlePlayerDeath(Player player)
    {
        if (!IsInProgress) return;

        CardModel[] allCombatCards =
            ((CardPile[])
            [
                player.PlayerCombatState!.Hand,
                player.PlayerCombatState.DrawPile,
                player.PlayerCombatState.DiscardPile,
                player.PlayerCombatState.ExhaustPile,
                player.PlayerCombatState.PlayPile
            ]).SelectMany(p => p.Cards).ToArray();

        await CardPileCmd.RemoveFromCombat(allCombatCards, false);

        await PlayerCmd.SetEnergy(0, player);
        await PlayerCmd.SetStars(0, player);
    }

    public void LoseCombat()
    {
        IsInProgress = false;
        ClimbManager.Instance.OnCombatLost(_state!);
    }

    /// <summary>
    /// DO NOT CALL THIS unless you're in this class or ModelTest.
    /// </summary>
    public async Task EndCombatInternal()
    {
        Player localPlayer = LocalContext.GetMe(_state)!;
        IClimbState climbState = _state!.ClimbState;
        CombatRoom room = (CombatRoom)climbState.CurrentRoom!;

        IsInProgress = false;
        PlayerActionsDisabled = false;
        _playersTakingExtraTurn.Clear();

        foreach (Player player in _state.Players)
        {
            await player.ReviveBeforeCombatEnd();
        }

        await Hook.AfterCombatEnd(_state!.ClimbState, _state, room);
        History.Clear();
        room.OnCombatEnded();
        CombatEnded?.Invoke(room);

        if (ClimbManager.Instance.NetService.Type != NetGameType.Replay)
        {
            string replayPath = SaveManager.Instance.GetProfileScopedPath("replays/latest.mcr");
            ClimbManager.Instance.CombatReplayWriter.WriteReplay(replayPath, true);
        }

        foreach (Player player in _state!.Players)
        {
            player.AfterCombatEnd();
        }

        await Hook.AfterCombatVictory(_state!.ClimbState, _state, room);

        NHoverTipSet.Clear();

        if (climbState.CurrentMapPointHistoryEntry != null)
        {
            climbState.CurrentMapPointHistoryEntry!.Rooms.Last().TurnsTaken = _state!.RoundNumber;
        }

        // The last boss has been defeated. Mark the win time for this run.
        if (room.RoomType == RoomType.Boss && climbState.CurrentActIndex == climbState.Acts.Count - 1)
        {
            ClimbManager.Instance.WinTime = ClimbManager.Instance.ClimbTime;
        }

        if (climbState.CurrentRoomCount == 1)
        {
            // Only save if this combat is the only room in the stack.
            // This avoids weird situations where we save in the middle of an event that has a combat in it, like Dense
            // Vegetation or Battleworn Dummy.
            room.MarkPreFinished();
            await SaveManager.Instance.SaveClimb(room);
            NMapScreen.Instance?.SetTravelEnabled(true);
        }

        SaveManager.Instance.UpdateProgressSaveAfterCombatWon(LocalContext.GetMe(_state)!, room);

        // Casey says, maybe encounters is simpler...
        AchievementsHelper.CheckForDefeatedAllEnemiesAchievement(climbState.Act, localPlayer);

        SaveManager.Instance.SaveProgressFile();

        if (room.RoomType == RoomType.Boss)
        {
            AchievementsHelper.AfterBossDefeated(localPlayer);
        }

        _state.MultiplayerScalingModel?.OnCombatFinished();

        CombatWon?.Invoke(room);

        // Unpause the action executor in case it was paused.
        ClimbManager.Instance.ActionExecutor.Unpause();

        // Unpause all player action queues in case they were paused.
        ClimbManager.Instance.ActionQueueSynchronizer.SetCombatState(ActionSynchronizerCombatState.NotInCombat);

        NClimbMusicController.Instance?.UpdateTrack();
    }

    public void RemoveCreature(Creature creature)
    {
        if (creature.IsMonster)
        {
            // Note: If we ever need this to be async/awaited, it should probably be moved to CreatureCmd.Remove
            creature.Monster!.BeforeRemovedFromRoom();
        }

        StateTracker.Unsubscribe(creature);
        CreaturesChanged?.Invoke(_state!);
    }

    public async Task<bool> CheckWinCondition()
    {
        if (IsAboutToEnd)
        {
            await EndCombatInternal();
            return true;
        }
        else
        {
            return false;
        }
    }

    private async Task ExecuteEnemyTurn(Func<Task>? actionDuringEnemyTurn = null)
    {
        // Need this check here in case there are no enemies
        if (!IsInProgress) return;

        if (actionDuringEnemyTurn != null)
        {
            await actionDuringEnemyTurn.Invoke();
        }

        foreach (Creature enemy in _state!.Enemies.ToList())
        {
            // The enemy may have been removed from combat during a previous enemy's move (for example, it may have died
            // to the Bent Nail relic), so make sure it's still in combat.
            // We can't just check if the enemy is dead here, because some enemies (like Decimillipede) perform moves
            // while dead.
            if (!_state.ContainsCreature(enemy)) continue;

            NCreature? node = NCombatRoom.Instance?.GetCreatureNode(enemy);

            if (node != null)
            {
                await node.PerformIntent();
            }

            await enemy.TakeTurn();

            await WaitForUnpause();
            await CheckWinCondition();

            if (!IsInProgress) return;
        }

        ClimbManager.Instance.ChecksumTracker.GenerateChecksum("After enemy turn end", null);

        await EndEnemyTurn();
    }

    private async Task AfterAllPlayersReadyToEndTurn()
    {
        EndingPlayerTurnPhaseOne = true;

        // This causes all player-driven actions to be cancelled until the next player turn. It is vital that this occurs
        // before any hooks are executed, otherwise timing issues can occur when executing non-hook actions interleaved
        // with hooks.
        ClimbManager.Instance.ActionQueueSynchronizer.SetCombatState(ActionSynchronizerCombatState.EndTurnPhaseOne);

        // If the end of turn was caused by a player card (like Void Form), we need to wait until that card play is
        // over before beginning the end of turn sequence. Otherwise, hooks and other end-of-turn execution might
        // interleave with the execution of the action.
        // We can't use ActionQueueSet.BecameEmpty because that would also block on the other player's hook actions,
        // like Well-Laid Plans, which might have been enqueued by them before we got here.
        await WaitUntilQueueIsEmptyOrWaitingOnNonPlayerDrivenAction();

        await EndPlayerTurnPhaseOneInternal();

        // Certain hooks in phase one can end combat, e.g. The Bomb. If combat has ended, we do not need this action.
        // If we are in replay mode, the replay enqueues this, not us.
        if (IsInProgress && ClimbManager.Instance.NetService.Type != NetGameType.Replay)
        {
            ClimbManager.Instance.ActionQueueSynchronizer.RequestEnqueue(
                new ReadyToBeginEnemyTurnAction(LocalContext.GetMe(_state!)!)
            );
        }

        EndingPlayerTurnPhaseOne = false;

        // Once ready actions are received from all players, this flow continues in AfterAllPlayersReadyToBeginEnemyTurn
    }

    private async Task WaitUntilQueueIsEmptyOrWaitingOnNonPlayerDrivenAction()
    {
        GameAction? runningAction = ClimbManager.Instance.ActionExecutor.CurrentlyRunningAction;
        if (runningAction == null || !ActionQueueSet.IsGameActionPlayerDriven(runningAction)) return;

        TaskCompletionSource completionSource = new();
        ClimbManager.Instance.ActionExecutor.AfterActionExecuted += AfterActionExecuted;
        await completionSource.Task;
        ClimbManager.Instance.ActionExecutor.AfterActionExecuted -= AfterActionExecuted;
        return;

        void AfterActionExecuted(GameAction action)
        {
            GameAction? readyAction = ClimbManager.Instance.ActionQueueSet.GetReadyAction();
            if (readyAction == null || !ActionQueueSet.IsGameActionPlayerDriven(readyAction)) completionSource.SetResult();
        }
    }

    /// <summary>
    /// DO NOT CALL THIS unless you're in this class or ModelTest.
    /// This calls all end-of-turn hooks that could require player choices to be made.
    /// </summary>
    public async Task EndPlayerTurnPhaseOneInternal()
    {
        if (_state!.CurrentSide != CombatSide.Player)
        {
            throw new InvalidOperationException($"EndPlayerTurn called while the current side is {_state.CurrentSide}!");
        }

        await WaitForUnpause();

        IsPlayPhase = false;
        await Hook.BeforeTurnEnd(_state, _state.CurrentSide);

        if (await CheckWinCondition())
        {
            // Combat ended while executing hooks, so we don't need to do anything else.
            return;
        }

        List<Task> playerEndTasks = [];

        foreach (Player player in _state.Players)
        {
            HookPlayerChoiceContext choiceContext = new(player, LocalContext.NetId!.Value);
            Task task = DoTurnEnd(player, choiceContext);
            await choiceContext.AssignTaskAndWaitForPauseOrCompletion(task);
            playerEndTasks.Add(task);
        }

        await Task.WhenAll(playerEndTasks);

        foreach (Player player in _state.Players)
        {
            await Hook.BeforeFlush(_state!, player);
        }

        ClimbManager.Instance.ChecksumTracker.GenerateChecksum("After player turn phase one end", null);

        // Check if combat is over, e.g. from The Bomb.
        await CheckWinCondition();
    }

    /// <summary>
    /// Executes turn end hooks for a player.
    /// If player choice occurs during this method, it uses the passed choice context. This way, each player's turn end
    /// runs independently of all others.
    /// </summary>
    private async Task DoTurnEnd(Player player, PlayerChoiceContext choiceContext)
    {
        await player.PlayerCombatState!.OrbQueue.BeforeTurnEnd(choiceContext);

        CardPile hand = PileType.Hand.GetPile(player);
        CardPile discardPile = PileType.Discard.GetPile(player);
        List<CardModel> turnEndCards = [];
        List<CardModel> etherealCards = [];

        foreach (CardModel card in hand.Cards)
        {
            if (card.HasTurnEndInHandEffect)
            {
                turnEndCards.Add(card);
            }
            else if (card.Keywords.Contains(CardKeyword.Ethereal) && Hook.ShouldEtherealTrigger(player.Creature.CombatState!, card))
            {
                etherealCards.Add(card);
            }
        }

        foreach (CardModel etherealCard in etherealCards)
        {
            await CardCmd.Exhaust(choiceContext, etherealCard, true);
        }

        foreach (CardModel card in turnEndCards)
        {
            await CardPileCmd.Add(card, PileType.Play);

            if (LocalContext.IsMe(player))
            {
                await Cmd.CustomScaledWait(0.3f, 0.6f, 0.6f);
            }

            await card.OnTurnEndInHand(choiceContext);

            // Make sure that if the end of turn card has ethereal, that it goes to the Ethereal pile
            // rather than the discard pile
            if (card.Keywords.Contains(CardKeyword.Ethereal))
            {
                await CardCmd.Exhaust(choiceContext, card, true);
            }
            else
            {
                await CardPileCmd.Add(card, discardPile);
            }
        }
    }

    private async Task EndEnemyTurnInternal()
    {
        await Hook.BeforeTurnEnd(_state!, _state!.CurrentSide);

        foreach (Player player in _state.Players)
        {
            player.PlayerCombatState!.EndOfTurnCleanup();
        }

        await Hook.AfterTurnEnd(_state!, _state!.CurrentSide);
    }

    private async Task AfterAllPlayersReadyToBeginEnemyTurn()
    {
        EndingPlayerTurnPhaseTwo = true;

        // This causes all player queues to become paused, and stops non-hook actions from being cancelled.
        // Player queues must be paused during this time, but still allow receiving actions from other players, because
        // of different execution speeds. If client 1 begins the player turn before client 2 (because 1 is on fast and 2
        // is on slow), then client 1 may begin playing cards while 2 is still in the enemy turn. Client 2 should start
        // executing those card plays as soon as their player turn begins.
        ClimbManager.Instance.ActionQueueSynchronizer.SetCombatState(ActionSynchronizerCombatState.NotPlayPhase);

        AboutToSwitchToEnemyTurn?.Invoke(_state!);

        // This finishes the ReadyToBeginEnemyTurnAction, in case combat is about to be finished
        await Task.Yield();

        await EndPlayerTurnPhaseTwoInternal();
        await SwitchFromPlayerToEnemySide();
        EndingPlayerTurnPhaseTwo = false;
    }

    /// <summary>
    /// DO NOT CALL THIS unless you're in this class or ModelTest.
    /// This does all the player state cleanup for the end of their turn. It must not call any hooks that might cause
    /// player choices to occur.
    /// </summary>
    public async Task EndPlayerTurnPhaseTwoInternal()
    {
        if (_state!.CurrentSide != CombatSide.Player)
        {
            throw new InvalidOperationException($"EndPlayerTurnPhaseTwo called while the current side is {_state.CurrentSide}!");
        }

        foreach (Player player in _state.Players)
        {
            CardPile hand = PileType.Hand.GetPile(player);
            List<CardModel> cardsToFlush = [];
            List<CardModel> cardsToRetain = [];

            foreach (CardModel card in hand.Cards)
            {
                if (card.ShouldRetainThisTurn)
                {
                    cardsToRetain.Add(card);
                }
                else
                {
                    cardsToFlush.Add(card);
                }
            }

            if (Hook.ShouldFlush(player.Creature.CombatState!, player))
            {
                // Flush hand.
                // We use Add instead of Discard here to avoid triggering "when a card is discarded" effects.
                await CardPileCmd.Add(cardsToFlush, PileType.Discard.GetPile(player));
            }

            foreach (CardModel card in cardsToRetain)
            {
                await Hook.AfterCardRetained(_state!, card);
            }

            player.PlayerCombatState!.EndOfTurnCleanup();
        }

        await Hook.AfterTurnEnd(_state!, _state!.CurrentSide);

        ClimbManager.Instance.ChecksumTracker.GenerateChecksum("after player turn phase two end", null);
    }

    /// <summary>
    /// DO NOT CALL THIS unless you're in this class or ModelTest.
    /// This switches from the player side to the enemy side, handling extra player turns if necessary.
    /// </summary>
    /// <param name="actionDuringEnemyTurn">Optional action to execute during the enemy turn. This is useful for tests.</param>
    public async Task SwitchFromPlayerToEnemySide(Func<Task>? actionDuringEnemyTurn = null)
    {
        _playersTakingExtraTurn.Clear();

        foreach (Player player in _state!.Players)
        {
            if (Hook.ShouldTakeExtraTurn(_state, player))
            {
                Log.Info($"Player {player.NetId} ({player.Character.Id.Entry}) is taking an extra turn");
                _playersTakingExtraTurn.Add(player);
            }
        }

        SwitchSides();

        foreach (Player player in _playersTakingExtraTurn)
        {
            await Hook.AfterTakingExtraTurn(_state!, player);
        }

        await WaitForUnpause();
        await StartTurn(actionDuringEnemyTurn);
    }

    private void SwitchSides()
    {
        if (_state!.CurrentSide == CombatSide.Player && _playersTakingExtraTurn.Count == 0)
        {
            _state.CurrentSide = CombatSide.Enemy;
        }
        else
        {
            _state.CurrentSide = CombatSide.Player;
            _state.RoundNumber++;
        }

        foreach (Creature creature in _state!.Creatures)
        {
            creature.OnSideSwitch();
        }

        TurnEnded?.Invoke(_state);
    }

    /// <summary>
    /// Pause combat.
    /// </summary>
    public void Pause()
    {
        // No pausing in tests.
        if (TestMode.IsOn) return;

        // No pausing outside of combat.
        if (!IsInProgress) return;

        IsPaused = true;
    }

    /// <summary>
    /// Un-pause combat.
    /// </summary>
    public void Unpause()
    {
        // No pausing in tests.
        if (TestMode.IsOn) return;

        IsPaused = false;
    }

    public async Task WaitForUnpause()
    {
        // No pausing in tests.
        if (TestMode.IsOn) return;

        while (IsPaused && IsInProgress)
        {
            await NGame.Instance!.ToSignal(NGame.Instance.GetTree(), SceneTree.SignalName.ProcessFrame);
        }
    }
}
