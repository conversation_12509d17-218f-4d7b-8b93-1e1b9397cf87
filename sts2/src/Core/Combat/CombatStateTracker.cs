using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Combat;

public class CombatStateTracker
{
    /// <summary>
    /// Fired whenever any part of the combat's state changes (card changes piles, power applied/removed, damage dealt,
    /// block gained/lost, etc.).
    /// </summary>
    public event Action<CombatState>? CombatStateChanged;

    private readonly CombatManager _combatManager;
    private Task? _combatStateChangedDeferredTask;

    // TODO: Maybe move this to an event or something.
    private CombatState? _state;

    public CombatStateTracker(CombatManager combatManager)
    {
        _combatManager = combatManager;
        _combatManager.CreaturesChanged += OnCreaturesChanged;
        _combatManager.TurnEnded += OnTurnEnded;
    }

    ~CombatStateTracker()
    {
        _combatManager.CreaturesChanged -= OnCreaturesChanged;
        _combatManager.TurnEnded -= OnTurnEnded;
    }

    // TODO: Maybe move this to an event or something.
    public void SetState(CombatState state)
    {
        _state = state;
    }

    public void Subscribe(CardModel card)
    {
        card.AfflictionChanged += OnCardValueChanged;
        card.EnchantmentChanged += OnCardValueChanged;
        card.EnergyCostChanged += OnCardValueChanged;
        card.ReplayCountChanged += OnCardValueChanged;
        card.Played += OnCardValueChanged;
        card.Drawn += OnCardValueChanged;
        card.StarCostChanged += OnCardValueChanged;
        card.Upgraded += OnCardValueChanged;
        card.Forged += OnCardValueChanged;
    }

    public void Unsubscribe(CardModel card)
    {
        card.AfflictionChanged -= OnCardValueChanged;
        card.EnchantmentChanged -= OnCardValueChanged;
        card.EnergyCostChanged -= OnCardValueChanged;
        card.ReplayCountChanged -= OnCardValueChanged;
        card.Played -= OnCardValueChanged;
        card.Drawn -= OnCardValueChanged;
        card.StarCostChanged -= OnCardValueChanged;
        card.Upgraded -= OnCardValueChanged;
        card.Forged -= OnCardValueChanged;
    }

    public void Subscribe(CardPile pile)
    {
        pile.ContentsChanged += OnCardPileContentsChanged;
    }

    public void Unsubscribe(CardPile pile)
    {
        pile.ContentsChanged -= OnCardPileContentsChanged;
    }

    public void Subscribe(Creature creature)
    {
        creature.BlockChanged += OnCreatureValueChanged;
        creature.CurrentHpChanged += OnCreatureValueChanged;
        creature.MaxHpChanged += OnCreatureValueChanged;
        creature.PowerApplied += OnPowerChanged;
        creature.PowerIncreased += OnPowerIncreased;
        creature.PowerDecreased += OnPowerChanged;
        creature.PowerRemoved += OnPowerChanged;
        creature.Died += OnCreatureChanged;
    }

    public void Unsubscribe(Creature creature)
    {
        creature.BlockChanged -= OnCreatureValueChanged;
        creature.CurrentHpChanged -= OnCreatureValueChanged;
        creature.MaxHpChanged -= OnCreatureValueChanged;
        creature.PowerApplied -= OnPowerChanged;
        creature.PowerIncreased -= OnPowerIncreased;
        creature.PowerDecreased -= OnPowerChanged;
        creature.PowerRemoved -= OnPowerChanged;
        creature.Died -= OnCreatureChanged;
    }

    public void Subscribe(PlayerCombatState combatState)
    {
        combatState.EnergyChanged += OnPlayerCombatStateValueChanged;
        combatState.StarsChanged += OnPlayerCombatStateValueChanged;
    }

    public void Unsubscribe(PlayerCombatState combatState)
    {
        combatState.EnergyChanged -= OnPlayerCombatStateValueChanged;
        combatState.StarsChanged -= OnPlayerCombatStateValueChanged;
    }

    private void OnCardPileContentsChanged() => NotifyCombatStateChanged(nameof(OnCardPileContentsChanged));
    private void OnCardValueChanged() => NotifyCombatStateChanged(nameof(OnCardValueChanged));
    private void OnCreatureValueChanged(int _, int __) => NotifyCombatStateChanged(nameof(OnCreatureValueChanged));
    private void OnCreaturesChanged(CombatState _) => NotifyCombatStateChanged(nameof(OnCreatureChanged));
    private void OnCreatureChanged(Creature _) => NotifyCombatStateChanged(nameof(OnCreaturesChanged));
    private void OnPlayerCombatStateValueChanged(int _, int __) => NotifyCombatStateChanged(nameof(OnPlayerCombatStateValueChanged));
    private void OnPowerChanged(PowerModel _) => NotifyCombatStateChanged(nameof(OnPowerIncreased));
    private void OnPowerIncreased(PowerModel _, int __) => NotifyCombatStateChanged(nameof(OnPowerIncreased));
    private void OnTurnEnded(CombatState _) => NotifyCombatStateChanged(nameof(OnTurnEnded));

    /// <summary>
    /// Notify the world that something in the combat state changed.
    /// </summary>
    /// <param name="caller">The name of the method that triggered this.</param>
    private void NotifyCombatStateChanged([SuppressMessage("ReSharper", "UnusedParameter.Local", Justification = "Useful for temporary debug logs")] string caller)
    {
        // This is not run in tests, and that should be fine. Only UI should be subscribing to this
        if (TestMode.IsOn)
        {
            if (CombatStateChanged != null)
            {
                throw new InvalidOperationException("Backend should not be subscribing to CombatStateChanged!");
            }

            return;
        }

        // This is an optimization. This method is called quite frequently, but since only visual updates depend on it,
        // we can defer it until end of frame and call it only once.
        if (_combatStateChangedDeferredTask == null || _combatStateChangedDeferredTask.IsCompleted)
        {
            _combatStateChangedDeferredTask = TaskHelper.RunSafely(CallCombatStateChangedDeferred());
        }
    }

    private async Task CallCombatStateChangedDeferred()
    {
        // Climb node can be null on the frame after the climb is abandoned.
        if (NClimb.Instance != null)
        {
            await NClimb.Instance.GetTree().ToSignal(NClimb.Instance.GetTree(), SceneTree.SignalName.ProcessFrame);
        }

        if (_state is { Creatures.Count: > 0 })
        {
            LocalContext.GetMe(_state)?.PlayerCombatState?.RecalculateCardValues();
            CombatStateChanged?.Invoke(_state);
        }
    }
}
