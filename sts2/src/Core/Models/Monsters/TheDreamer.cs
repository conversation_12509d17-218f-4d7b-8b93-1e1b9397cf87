using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TheDreamer : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 555, 555);
    public override int MaxInitialHp => MinInitialHp;

    private int SnoreDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 12, 10);

    private int FendOffBlock => 30;
    private int DreamBlock => 55;

    private int RunimateStrength => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 4, 3);

    // This gets set in GenerateMoveStateMachine, which is effectively a constructor.
    private MoveState _dreamState = default!;

    private MoveState DreamState
    {
        get => _dreamState;
        set
        {
            AssertMutable();
            _dreamState = value;
        }
    }

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Magic;

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<NightTerror>(Creature, 300, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];
        MoveState fendOffState = new("FEND_OFF_MOVE", FendOffMove, new DefendIntent());
        MoveState snoreState = new("SNORE_MOVE", SnoreMove, new SingleAttackIntent(SnoreDamage));
        MoveState ruminateState = new("RUMINATE_MOVE", RuminateMove, new BuffIntent());
        DreamState = new("DREAM_MOVE", DreamMove, new BuffIntent(), new DefendIntent());

        fendOffState.FollowUpState = snoreState;
        snoreState.FollowUpState = ruminateState;
        ruminateState.FollowUpState = fendOffState;

        DreamState.FollowUpState = snoreState;

        states.Add(fendOffState);
        states.Add(snoreState);
        states.Add(ruminateState);
        states.Add(DreamState);

        return new MonsterMoveStateMachine(states, fendOffState);
    }

    private async Task FendOffMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.GainBlock(Creature, FendOffBlock, BlockProps.monsterMove, null);
    }

    private async Task SnoreMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(SnoreDamage)
            .FromMonster(this)
            .WithAttackerAnim(CreatureAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute(null);
    }

    private async Task RuminateMove(IReadOnlyList<Creature> targets)
    {
        List<Creature> allies = Creature.CombatState!.GetTeammatesOf(Creature).Where(c => c != Creature).ToList();
        if (allies.Count == 0)
        {
            return;
        }

        await PowerCmd.Apply<Strength>(Rng.NextItem(allies)!, RunimateStrength, Creature, null);
    }


    public void TriggerDreamState()
    {
        SetMoveImmediate(DreamState, true);
    }

    private async Task DreamMove(IReadOnlyList<Creature> targets)
    {
        List<Creature> allies = Creature.CombatState!.GetTeammatesOf(Creature).Where(c => c != Creature).ToList();
        if (allies.Count == 0)
        {
            return;
        }

        foreach (Creature teammate in allies)
        {
            await CreatureCmd.GainMaxHp(teammate, 20);
        }

        await CreatureCmd.GainBlock(Creature, DreamBlock, BlockProps.monsterMove, null);
    }
}
