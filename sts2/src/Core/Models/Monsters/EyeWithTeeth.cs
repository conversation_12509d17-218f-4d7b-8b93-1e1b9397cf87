using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Bindings.MegaSpine;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class EyeWithTeeth : MonsterModel
{
    public override int MinInitialHp => 6;
    public override int MaxInitialHp => MinInitialHp;

    private const int _distractAmount = 3;
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Magic;

    public override bool ShouldDisappearFromDoom => false;

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Illusion>(Creature, 1, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];
        MoveState distractState = new("DISTRACT_MOVE", DistractMove, new StatusIntent(_distractAmount));
        distractState.FollowUpState = distractState;

        states.Add(distractState);
        return new MonsterMoveStateMachine(states, distractState);
    }

    private async Task DistractMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(AttackSfx);
        await CreatureCmd.TriggerAnim(Creature, CreatureAnimator.attackTrigger, 0.7f);

        VfxCmd.PlayOnCreatureCenters(targets, VfxCmd.slashPath);
        await CardPileCmd.AddToCombatAndPreview<Dazed>(targets, PileType.Discard, _distractAmount, false);
    }

    public override CreatureAnimator GenerateAnimator(MegaSprite controller)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        // TODO: Add death anim once we have one that doesn't make the monster disappear.

        attackAnim.NextState = idleAnim;

        CreatureAnimator animator = new(idleAnim, controller);

        animator.AddAnyState(CreatureAnimator.attackTrigger, attackAnim);
        animator.AddAnyState(
            CreatureAnimator.deathTrigger,
            deathAnim,
            // Only play the death animation if all primary enemies are dead.
            () => !CombatState.GetTeammatesOf(Creature).Any(t => t is { IsPrimaryEnemy: true, IsAlive: true })
        );

        return animator;
    }
}
