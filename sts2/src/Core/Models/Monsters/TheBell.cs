using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TheBell : MonsterModel
{
    public override bool ShouldDisappearFromDoom => false;

    private const string _clangMoveId = "CLANG_MOVE";

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 66, 61);
    public override int MaxInitialHp => MinInitialHp;

    private int ClangDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 18, 16);
    private int RingDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 12, 11);
    private const int _ringStatusCount = 2;

    private int CacophonyDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 6, 5);

    private const int _cacophonyTimes = 3;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.ArmorBig;

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        Illusion illusion = (await PowerCmd.Apply<Illusion>(Creature, 1, Creature, null))!;
        illusion.FollowUpStateId = _clangMoveId;
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];
        MoveState clangState = new(_clangMoveId, ClangMove, new SingleAttackIntent(ClangDamage));
        MoveState ringState = new("RING_MOVE", RingMove, new SingleAttackIntent(RingDamage), new StatusIntent(_ringStatusCount), new DebuffIntent());
        MoveState cacophonyState = new("CACOPHONY_MOVE", CacophonyMove, new MultiAttackIntent(CacophonyDamage, _cacophonyTimes));


        clangState.FollowUpState = ringState;
        ringState.FollowUpState = cacophonyState;
        cacophonyState.FollowUpState = clangState;

        states.Add(clangState);
        states.Add(ringState);
        states.Add(cacophonyState);

        return new MonsterMoveStateMachine(states, clangState);
    }

    private async Task ClangMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(ClangDamage)
            .FromMonster(this)
            .WithAttackerAnim(CreatureAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute(null);
    }

    private async Task RingMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(RingDamage)
            .FromMonster(this)
            .WithAttackerAnim(CreatureAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute(null);

        await PowerCmd.Apply<Frail>(targets, 1, Creature, null);

        // HACK: have to do this manually rather than CardPileCmd.AddAndPreview
        // because we need to preview cards going to 2 separate piles
        foreach (Creature target in targets)
        {
            Player player = target.Player ?? target.PetOwner!;

            CardPileAddResult[] statusCards = new CardPileAddResult[_ringStatusCount];

            CardModel card1 = CombatState.CreateCard<Dazed>(player);
            statusCards[0] = await CardPileCmd.AddGeneratedCardToCombat(card1, PileType.Discard, false);

            CardModel card2 = CombatState.CreateCard<Dazed>(player);
            statusCards[1] = await CardPileCmd.AddGeneratedCardToCombat(card2, PileType.Draw, false, CardPilePosition.Random);

            if (LocalContext.IsMe(player))
            {
                CardCmd.PreviewCardPileAdd(statusCards);
                await Cmd.Wait(1f);
            }
        }
    }

    private async Task CacophonyMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(CacophonyDamage)
            .WithHitCount(_cacophonyTimes)
            .FromMonster(this)
            .WithAttackerAnim(CreatureAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute(null);
    }
}
