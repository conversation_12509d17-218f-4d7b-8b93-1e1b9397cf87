using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TheArm : MonsterModel
{
    private const string _swipeBranchId = "SWIPE_BRANCH";

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 55, 50);
    public override int MaxInitialHp => MinInitialHp;

    private int PunchDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 11, 10);
    private int PunchRepeat => 2;

    private int WrestleDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 13, 12);

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Magic;
    public override bool ShouldDisappearFromDoom => false;

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Illusion>(Creature, 1, Creature, null);

        Illusion illusion = (await PowerCmd.Apply<Illusion>(Creature, 1, Creature, null))!;
        illusion.FollowUpStateId = _swipeBranchId;
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];
        MoveState grabState = new("GRAB_MOVE", GrabMove, new CardDebuffIntent());
        MoveState punchState = new("PUNCH_MOVE", PunchMove, new MultiAttackIntent(PunchDamage, PunchRepeat));
        MoveState wrestleState = new("WRESTLE_MOVE", WrestleMove, new SingleAttackIntent(WrestleDamage));

        ConditionalBranchState swipeBranch = new(_swipeBranchId);
        swipeBranch.AddState(punchState, (_, _) => Creature.HasPower<Grabbed>());
        swipeBranch.AddState(grabState, (_, _) => !Creature.HasPower<Grabbed>());

        grabState.FollowUpState = punchState;
        punchState.FollowUpState = wrestleState;
        wrestleState.FollowUpState = punchState;

        states.Add(grabState);
        states.Add(punchState);
        states.Add(wrestleState);
        states.Add(swipeBranch);

        return new MonsterMoveStateMachine(states, grabState);
    }

    private async Task GrabMove(IReadOnlyList<Creature> targets)
    {
        // steals a card
        NCreature? creatureNode = NCombatRoom.Instance?.GetCreatureNode(Creature);

        List<CardModel> cardsToRemove = [];
        foreach (Creature target in targets)
        {
            List<CardModel> candidateCards = CardPile.GetCards(target.Player ?? target.PetOwner!, PileType.Draw, PileType.Discard)
                .Where(c => c.Rarity is CardRarity.Rare or CardRarity.Uncommon or CardRarity.Common)
                .ToList();

            if (candidateCards.Count == 0)
            {
                candidateCards = CardPile.GetCards(target.Player ?? target.PetOwner!, PileType.Draw, PileType.Discard)
                    .ToList();
            }

            if (candidateCards.Count == 0)
            {
                continue;
            }

            candidateCards.StableShuffle(ClimbRng.CombatCardGeneration);

            CardModel cardToSteal = candidateCards.First();
            await CardPileCmd.RemoveFromCombat(cardToSteal, false);
            cardsToRemove.Add(cardToSteal);
        }

        foreach (CardModel cardToSteal in cardsToRemove)
        {
            if (creatureNode != null && LocalContext.IsMine(cardToSteal))
            {
                Marker2D? cardHolder = creatureNode.GetSpecialNode<Marker2D>("%StolenCardPos");

                if (cardHolder != null)
                {
                    NCard card = NCard.Create(cardToSteal)!;
                    cardHolder.AddChildSafely(card);
                    card.Position += card.Size * 0.5f;
                    card.UpdateVisuals(PileType.Deck, CardPreviewMode.Normal);
                }
            }

            Grabbed grabbed = (Grabbed)ModelDb.Power<Grabbed>().ToMutable();
            grabbed.Target = cardToSteal.Owner.Creature;
            grabbed.StolenCard = cardToSteal;

            await PowerCmd.Apply(grabbed, Creature, 1, Creature, null);
        }
    }

    private async Task PunchMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(PunchDamage)
            .WithHitCount(PunchRepeat)
            .FromMonster(this)
            .WithAttackerAnim(CreatureAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute(null);
    }

    private async Task WrestleMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(WrestleDamage)
            .FromMonster(this)
            .WithAttackerAnim(CreatureAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute(null);
    }
}
