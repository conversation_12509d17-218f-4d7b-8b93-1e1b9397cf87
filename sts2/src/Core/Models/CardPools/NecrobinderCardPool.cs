using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Timeline.Epochs;
using MegaCrit.Sts2.Core.Unlocks;

namespace MegaCrit.Sts2.Core.Models.CardPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class NecrobinderCardPool : CardPoolModel
{
    public override string Title => "necrobinder";
    public override string EnergyColorName => Necrobinder.energyColorName;
    public override string CardFrameMaterialPath => "card_frame_pink";
    public override Color DeckEntryCardColor => new("CD4EED");
    public override Color EnergyOutlineColor => new("803367");
    public override bool IsColorless => false;

    protected override CardModel[] GenerateAllCards() =>
    [
        ModelDb.Card<Afterlife>(),
        ModelDb.Card<BansheesCry>(),
        ModelDb.Card<BlightStrike>(),
        ModelDb.Card<Bodyguard>(),
        ModelDb.Card<BoneShards>(),
        ModelDb.Card<BorrowedTime>(),
        ModelDb.Card<Bury>(),
        ModelDb.Card<CallOfTheVoid>(),
        ModelDb.Card<Camaraderie>(),
        ModelDb.Card<CaptureSpirit>(),
        ModelDb.Card<CarveGhost>(),
        ModelDb.Card<Cleanse>(),
        ModelDb.Card<Countdown>(),
        ModelDb.Card<DanseMacabre>(),
        ModelDb.Card<Deathbringer>(),
        ModelDb.Card<DeathsDoor>(),
        ModelDb.Card<DeathMarch>(),
        ModelDb.Card<DeathsVisage>(),
        ModelDb.Card<Debilitate>(),
        ModelDb.Card<DefendNecrobinder>(),
        ModelDb.Card<Defile>(),
        ModelDb.Card<Delay>(),
        ModelDb.Card<Demesne>(),
        ModelDb.Card<DevourLife>(),
        ModelDb.Card<Dirge>(),
        ModelDb.Card<DrainPower>(),
        ModelDb.Card<Dredge>(),
        ModelDb.Card<Eidolon>(),
        ModelDb.Card<Empower>(),
        ModelDb.Card<EndOfDays>(),
        ModelDb.Card<EnfeeblingTouch>(),
        ModelDb.Card<Eradicate>(),
        ModelDb.Card<Fear>(),
        ModelDb.Card<Fetch>(),
        ModelDb.Card<Flatten>(),
        ModelDb.Card<ForbiddenGrimoire>(),
        ModelDb.Card<Friendship>(),
        ModelDb.Card<GraveWarden>(),
        ModelDb.Card<Graveblast>(),
        ModelDb.Card<Hang>(),
        ModelDb.Card<Haunt>(),
        ModelDb.Card<Invoke>(),
        ModelDb.Card<Lethality>(),
        ModelDb.Card<MakeUse>(),
        ModelDb.Card<Melancholy>(),
        ModelDb.Card<Misery>(),
        ModelDb.Card<NecroMastery>(),
        ModelDb.Card<NegativePulse>(),
        ModelDb.Card<Neurosurge>(),
        ModelDb.Card<NoEscape>(),
        ModelDb.Card<Oblivion>(),
        ModelDb.Card<Pagestorm>(),
        ModelDb.Card<Parse>(),
        ModelDb.Card<Poke>(),
        ModelDb.Card<Protector>(),
        ModelDb.Card<PullAggro>(),
        ModelDb.Card<PullFromBelow>(),
        ModelDb.Card<Putrefy>(),
        ModelDb.Card<Rattle>(),
        ModelDb.Card<Reanimate>(),
        ModelDb.Card<Reap>(),
        ModelDb.Card<ReaperForm>(),
        ModelDb.Card<Restrain>(),
        ModelDb.Card<RightHandHand>(),
        ModelDb.Card<Sacrifice>(),
        ModelDb.Card<Scourge>(),
        ModelDb.Card<SculptingStrike>(),
        ModelDb.Card<Seance>(),
        ModelDb.Card<SentryMode>(),
        ModelDb.Card<Severance>(),
        ModelDb.Card<SharedFate>(),
        ModelDb.Card<Shroud>(),
        ModelDb.Card<SleightOfFlesh>(),
        ModelDb.Card<Snap>(),
        ModelDb.Card<SoulStorm>(),
        ModelDb.Card<Sow>(),
        ModelDb.Card<SpiritOfAsh>(),
        ModelDb.Card<StrikeNecrobinder>(),
        ModelDb.Card<Symbiosis>(),
        ModelDb.Card<TagIn>(),
        ModelDb.Card<TheScythe>(),
        ModelDb.Card<TimesUp>(),
        ModelDb.Card<Transfigure>(),
        ModelDb.Card<Twist>(),
        ModelDb.Card<Undeath>(),
        ModelDb.Card<Unleash>(),
        ModelDb.Card<Veilpiercer>(),
        ModelDb.Card<Wisp>()
    ];

    /// <summary>
    /// Returns a list of cards but prunes out cards if the associated Epochs aren't revealed.
    /// </summary>
    public override IEnumerable<CardModel> GetUnlockedCards(UnlockState unlockState)
    {
        List<CardModel> retVal = AllCards.ToList();

        if (!unlockState.IsEpochRevealed<Necrobinder2Epoch>())
        {
            retVal.RemoveAll(c => Necrobinder2Epoch.Cards.Any(card => card.Id == c.Id));
        }

        if (!unlockState.IsEpochRevealed<Necrobinder5Epoch>())
        {
            retVal.RemoveAll(c => Necrobinder5Epoch.Cards.Any(card => card.Id == c.Id));
        }

        if (!unlockState.IsEpochRevealed<Necrobinder7Epoch>())
        {
            retVal.RemoveAll(c => Necrobinder7Epoch.Cards.Any(card => card.Id == c.Id));
        }

        return retVal;
    }
}
