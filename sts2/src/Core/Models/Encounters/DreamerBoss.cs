using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Bindings.MegaSpine;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DreamerBoss : EncounterModel
{
    public override RoomType RoomType => RoomType.Boss;

    private const string _dreamerSlot = "dreamer";
    private const string _illusionSlotPrefix = "illusion";

    public override IReadOnlyList<string> Slots =>
    [
        $"{_illusionSlotPrefix}1",
        $"{_illusionSlotPrefix}2",
        _dreamerSlot
    ];
    public override MegaSkeletonDataResource? BossNodeSpineResource => null;

    public override string BossNodePath => $"res://images/packed/map/placeholder/{Id.Entry.ToLower()}_icon.png";

    public override string CustomBgm => "event:/music/act3_boss_queen";

    public override float GetCameraScaling(CombatState combatState) => 0.90f;
    public override Vector2 GetCameraOffset(CombatState combatState) => Vector2.Down * 60f;
    protected override bool HasCustomBackground => false;
    public override bool HasScene => true;

    public override IEnumerable<MonsterModel> AllPossibleMonsters =>
    [
        ModelDb.Monster<TheArm>(),
        ModelDb.Monster<TheBell>(),
        ModelDb.Monster<TheDreamer>()
    ];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (ModelDb.Monster<TheArm>().ToMutable(), $"{_illusionSlotPrefix}1"),
        (ModelDb.Monster<TheBell>().ToMutable(), $"{_illusionSlotPrefix}2"),
        (ModelDb.Monster<TheDreamer>().ToMutable(), _dreamerSlot),
    ];
}
