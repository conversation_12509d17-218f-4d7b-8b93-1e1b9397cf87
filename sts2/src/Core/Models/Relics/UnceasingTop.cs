using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class UnceasingTop : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Rare;

    public override async Task AfterHandEmptied(PlayerChoiceContext choiceContext, Player player)
    {
        // Only trigger during the part of the player's turn where they can manually play cards.
        if (!CombatManager.Instance.IsPlayPhase) return;

        if (player != Owner) return;

        Flash();
        await CardPileCmd.Draw(choiceContext, player);
    }
}
