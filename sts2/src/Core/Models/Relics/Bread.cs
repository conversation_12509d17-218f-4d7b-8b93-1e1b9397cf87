using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Bread : RelicModel
{
    private const string _gainEnergyKey = "GainEnergy";
    private const string _loseEnergyKey = "LoseEnergy";
    public override RelicRarity Rarity => RelicRarity.Shop;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new EnergyVar(_gainEnergyKey, 1),
        new EnergyVar(_loseEnergyKey, 3)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.ForEnergy(this)];

    public override async Task AfterSideTurnStart(PlayerChoiceContext choiceContext, CombatSide side, CombatState combatState)
    {
        if (side != Owner.Creature.Side) return;
        if (combatState.RoundNumber == 1)
        {
            await PlayerCmd.LoseEnergy(DynamicVars[_loseEnergyKey].BaseValue, Owner);
        }

        await PlayerCmd.GainEnergy(DynamicVars[_gainEnergyKey].IntValue, Owner);
    }
}
