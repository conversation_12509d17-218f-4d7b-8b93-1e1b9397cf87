using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class VenerableTeaSet : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Common;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new EnergyVar(2)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.ForEnergy(this)];

    private bool _gainEnergyInNextCombat;

    [SavedProperty]
    public bool GainEnergyInNextCombat
    {
        get => _gainEnergyInNextCombat;
        set
        {
            AssertMutable();
            if (_gainEnergyInNextCombat == value) return;

            _gainEnergyInNextCombat = value;
            Status = _gainEnergyInNextCombat ? RelicStatus.Active : RelicStatus.Normal;
        }
    }

    public override Task AfterRoomEntered(AbstractRoom room)
    {
        if (room is not RestSiteRoom) return Task.CompletedTask;

        GainEnergyInNextCombat = true;

        return Task.CompletedTask;
    }

    public override Task AfterEnergyReset(Player player)
    {
        if (Owner != player) return Task.CompletedTask;
        if (!GainEnergyInNextCombat) return Task.CompletedTask;

        Flash();
        PlayerCmd.GainEnergy(DynamicVars.Energy.BaseValue, Owner);
        GainEnergyInNextCombat = false;

        return Task.CompletedTask;
    }
}
