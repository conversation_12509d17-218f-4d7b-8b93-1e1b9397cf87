using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class PaelsTears : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Ancient;

    private bool _hadLeftoverEnergy;

    private bool HadLeftoverEnergy
    {
        get => _hadLeftoverEnergy;
        set
        {
            AssertMutable();
            _hadLeftoverEnergy = value;
        }
    }

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new EnergyVar(2)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.ForEnergy(this)];

    public override Task BeforeTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != CombatSide.Player) return Task.CompletedTask;

        HadLeftoverEnergy = Owner.PlayerCombatState!.Energy > 0;
        return Task.CompletedTask;
    }

    public override async Task AfterSideTurnStart(CombatSide side, CombatState combatState)
    {
        if (side != Owner.Creature.Side) return;
        if (!HadLeftoverEnergy) return;

        Flash();
        await PlayerCmd.GainEnergy(DynamicVars.Energy.BaseValue, Owner);
    }

    public override Task AfterCombatEnd(CombatRoom room)
    {
        HadLeftoverEnergy = false;
        return Task.CompletedTask;
    }
}
