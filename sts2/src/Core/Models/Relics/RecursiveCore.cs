using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class RecursiveCore : RelicModel
{
    private const string _starterRelicKey = "StarterRelic";
    private const string _upgradedRelicKey = "UpgradedRelic";

    public override RelicRarity Rarity => RelicRarity.Ancient;


    private static Dictionary<ModelId, RelicModel> RefinementUpgrades => new()
    {
        { ModelDb.Relic<BurningBlood>().Id, ModelDb.Relic<BlackBlood>() },
        { ModelDb.Relic<RingOfTheSnake>().Id, ModelDb.Relic<RingOfTheDrake>() },
        { ModelDb.Relic<TheThrone>().Id, ModelDb.Relic<GalacticDust>() },
        { ModelDb.Relic<BoundPhylactery>().Id, ModelDb.Relic<PhylacteryUnbound>() },
        { ModelDb.Relic<CrackedCore>().Id, ModelDb.Relic<InfusedCore>() }
    };

    private ModelId? _starterRelic;

    [SavedProperty]
    public ModelId? StarterRelic
    {
        get => _starterRelic;
        set
        {
            AssertMutable();

            if (_starterRelic != null) throw new InvalidOperationException($"Recursive Core setup called twice!");

            _starterRelic = value;

            if (_starterRelic != null)
            {
                RelicModel starterModel = SaveUtil.RelicOrDeprecated(_starterRelic);
                _extraHoverTips.AddRange(starterModel.HoverTips);
                ((StringVar)DynamicVars[_starterRelicKey]).StringValue = starterModel.Title.GetFormattedText();
            }
        }
    }

    private ModelId? _upgradedRelic;

    [SavedProperty]
    public ModelId? UpgradedRelic
    {
        get => _upgradedRelic;
        set
        {
            AssertMutable();

            if (_upgradedRelic != null) throw new InvalidOperationException($"Recursive Core setup called twice!");

            _upgradedRelic = value;

            if (_upgradedRelic != null)
            {
                RelicModel relicModel = SaveUtil.RelicOrDeprecated(_upgradedRelic);
                _extraHoverTips.AddRange(relicModel.HoverTips);
                ((StringVar)DynamicVars[_upgradedRelicKey]).StringValue = relicModel.Title.GetFormattedText();
            }
        }
    }

    private List<IHoverTip> _extraHoverTips = [];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => _extraHoverTips;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new StringVar(_starterRelicKey),
        new StringVar(_upgradedRelicKey)
    ];

    protected override void AfterCloned()
    {
        base.AfterCloned();
        _extraHoverTips = [];
    }

    private RelicModel? GetStarterRelic(Player p) => p.Relics.FirstOrDefault(r => r.Rarity == RelicRarity.Starter);

    private RelicModel GetUpgradedStarterRelic(RelicModel starterRelic)
    {
        if (RefinementUpgrades.TryGetValue(starterRelic.Id, out RelicModel? upgradeRelic))
        {
            return upgradeRelic;
        }
        else
        {
            return ModelDb.Relic<Circlet>().ToMutable();
        }
    }

    /// <summary>
    /// Sets up the upgraded starter relic based off of the player.
    /// </summary>
    /// <param name="p">The player who we are upgrading the starter relic for</param>
    /// <returns>Returns false if player doesnt have the original starter relic.</returns>
    public bool SetupForPlayer(Player p)
    {
        AssertMutable();

        RelicModel? starterRelic = GetStarterRelic(p);
        if (starterRelic != null)
        {
            StarterRelic = starterRelic.Id;
            UpgradedRelic = GetUpgradedStarterRelic(starterRelic).Id;
            return true;
        }

        return false;
    }

    public void SetupForTests(ModelId starterRelic, ModelId upgradedRelic)
    {
        AssertMutable();

        StarterRelic = starterRelic;
        UpgradedRelic = upgradedRelic;
    }

    public override async Task AfterObtained()
    {
        RelicModel starterRelic = Owner.GetRelicById(StarterRelic!)!;
        RelicModel upgradeRelic = ModelDb.GetById<RelicModel>(UpgradedRelic!).ToMutable();
        await RelicCmd.Replace(starterRelic, upgradeRelic);
    }
}
