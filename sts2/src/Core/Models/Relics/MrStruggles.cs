using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class MrStruggles : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Event;

    public override async Task AfterSideTurnStart(CombatSide side, CombatState combatState)
    {
        if (side != Owner.Creature.Side) return;

        Flash();
        await CreatureCmd.Damage(
            combatState.HittableEnemies,
            combatState.RoundNumber,
            DamageProps.nonCardUnpowered,
            Owner.Creature
        );
    }
}
