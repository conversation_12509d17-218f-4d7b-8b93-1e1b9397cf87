using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Regalite : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Uncommon;

    private bool _activatedThisTurn;

    private bool ActivatedThisTurn
    {
        get => _activatedThisTurn;
        set
        {
            AssertMutable();
            _activatedThisTurn = value;
        }
    }

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new EnergyVar(1),
    ];

    public override Task AfterSideTurnStart(CombatSide side, CombatState combatState)
    {
        if (side != Owner.Creature.Side) return Task.CompletedTask;

        ActivatedThisTurn = false;
        Status = RelicStatus.Active;

        return Task.CompletedTask;
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card.Owner != Owner) return;
        if (!CombatManager.Instance.IsInProgress) return;
        if (!card.Pool.IsColorless) return;
        if (ActivatedThisTurn) return;

        Flash();
        await PlayerCmd.GainEnergy(DynamicVars.Energy.BaseValue, Owner);
        ActivatedThisTurn = true;
        Status = RelicStatus.Disabled;
    }

    public override Task AfterCombatEnd(CombatRoom room)
    {
        ActivatedThisTurn = false;
        Status = RelicStatus.Normal;
        return Task.CompletedTask;
    }
}
