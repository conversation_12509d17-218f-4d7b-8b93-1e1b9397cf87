using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Bindings.MegaSpine;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Models;

public abstract class MonsterModel : AbstractModel
{
    public static readonly Vector2 defaultDeathVfxPadding = 1.2f * Vector2.One;
    public const string stunnedMoveId = "STUNNED";

    private const string _locTableName = "monsters";

    public override bool ShouldReceiveCombatHooks => true;

    public virtual LocString Title => L10NMonsterLookup($"{Id.Entry}.name");

    public abstract int MinInitialHp { get; }
    public abstract int MaxInitialHp { get; }

    public virtual bool IsHealthBarVisible => true;

    /// <summary>
    /// At time of death, a SubViewport is created with the bounds of the creature's spine body in its current pose.
    /// If the creature needs additional padding (usually because the death animation changes its bounds), this property
    /// can be adjusted so that the spawned SubViewport is larger.
    /// </summary>
    public virtual Vector2 ExtraDeathVfxPadding => defaultDeathVfxPadding;

    /// <summary>
    /// For specific creatures that are very close, this value can be made some positive value so that their health bars
    /// do not overlap each other.
    /// </summary>
    public virtual float HpBarSizeReduction => 0f;

    protected virtual string VisualsPath => SceneHelper.GetScenePath($"creature_visuals/{Id.Entry.ToLower()}");

    public virtual IEnumerable<string> AssetPaths
    {
        get
        {
            List<string> assets = [VisualsPath];
            foreach (AbstractIntent intent in GetIntents())
            {
                assets.AddRange(intent.AssetPaths);
            }

            return assets;
        }
    }

    private Rng? _rng;

    /// <summary>
    /// This RNG should be used for things that are nice to have synced between players. It should be deterministic as
    /// long as you only use it in deterministic contexts.
    /// It should ONLY be set in <see cref="CombatState.CreateCreature"/>.
    /// </summary>
    public Rng Rng
    {
        get => IsMutable ? _rng! : Rng.Chaotic;
        set
        {
            AssertMutable();
            _rng = value;
        }
    }

    private ClimbRngSet? _climbRng;

    /// <summary>
    /// Climb-scoped RNG set for the climb that this monster is in.
    /// </summary>
    public ClimbRngSet ClimbRng
    {
        get => _climbRng!;
        set
        {
            AssertMutable();
            if (_climbRng != null) throw new InvalidOperationException("ClimbRng has already been set!");

            _climbRng = value;
        }
    }

    private bool _isPerformingMove;

    public bool IsPerformingMove
    {
        get => _isPerformingMove;
        private set
        {
            AssertMutable();
            _isPerformingMove = value;
        }
    }

    public IEnumerable<LocString> MoveNames => LocManager.Instance.GetTable(_locTableName).GetLocStringsWithPrefix($"{Id.Entry}.moves");

    public NCreatureVisuals CreateVisuals() => PreloadManager.Cache.GetScene(VisualsPath).Instantiate<NCreatureVisuals>();

    public virtual string BestiaryAttackAnimId => AnimState.attackAnim;

    protected virtual string AttackSfx => $"event:/sfx/enemy/enemy_attacks/{Id.Entry.ToLower()}/{Id.Entry.ToLower()}_attack";
    protected virtual string CastSfx => $"event:/sfx/enemy/enemy_attacks/{Id.Entry.ToLower()}/{Id.Entry.ToLower()}_cast";

    public virtual string DeathSfx => $"event:/sfx/enemy/enemy_attacks/{Id.Entry.ToLower()}/{Id.Entry.ToLower()}_die";

    public virtual bool HasDeathSfx => true;

    /// <summary>
    /// Should the default fade animation be played after this monster dies?
    /// Usually true, but false for monsters with certain special death animations (Decimillipede, etc.) or that we want
    /// to keep around after death (Osty, etc.).
    /// </summary>
    public virtual bool ShouldFadeAfterDeath => true;

    public virtual bool ShouldDisappearFromDoom => true;

    public virtual bool CanChangeScale => true;

    public virtual DamageSfxType TakeDamageSfxType => DamageSfxType.Armor;

    public virtual string TakeDamageSfx => $"event:/sfx/enemy/enemy_impact_enemy_size/enemy_impact_{TakeDamageSfxType.ToString().ToLower()}";

    private Creature? _creature;

    /// <summary>
    /// The creature that represents this monster in combat.
    /// Will technically be null on a canonical monster model, but we should never be checking that, so we leave this as
    /// non-nullable for convenience.
    /// </summary>
    public Creature Creature
    {
        get => _creature ?? throw new InvalidOperationException("Creature was accessed before it was set.");
        set
        {
            AssertMutable();
            if (_creature != null) throw new InvalidOperationException($"Monster {Id.Entry} already has a creature.");

            _creature = value;
        }
    }

    /// <summary>
    /// The CombatState that this monster's creature exists in.
    /// Will technically be null on a canonical monster model, but we should never be checking that, so we leave this as
    /// non-nullable for convenience.
    /// </summary>
    public CombatState CombatState => Creature.CombatState!;

    private List<AbstractIntent> GetIntents()
    {
        List<AbstractIntent> intents = [];
        MonsterMoveStateMachine moveStateMachine = GenerateMoveStateMachine();
        foreach (MonsterState monsterState in moveStateMachine.States.Values)
        {
            if (monsterState.IsMove && monsterState is MoveState moveState)
            {
                intents.AddRange(moveState.Intents);
            }
        }

        return intents;
    }

    private MonsterMoveStateMachine? _moveStateMachine;

    /// <summary>
    /// Get this monster's move state machine.
    /// Null for canonical monster models.
    /// </summary>
    public MonsterMoveStateMachine? MoveStateMachine
    {
        get => _moveStateMachine;
        private set
        {
            AssertMutable();

            if (MoveStateMachine != null)
            {
                throw new InvalidOperationException($"{Id.Entry}'s move state machine has already been set");
            }

            _moveStateMachine = value;
        }
    }

    public MoveState NextMove { get; private set; } = new();

    public bool IntendsToAttack => NextMove.Intents.Any(intent => intent.IntentType is IntentType.Attack or IntentType.DeathBlow);

    private bool _spawnedThisTurn;

    public bool SpawnedThisTurn
    {
        get => _spawnedThisTurn;
        private set
        {
            AssertMutable();
            _spawnedThisTurn = value;
        }
    }

    private MonsterModel _canonicalInstance = default!;

    public MonsterModel CanonicalInstance
    {
        get => IsMutable ? _canonicalInstance : this;
        private set
        {
            AssertMutable();
            _canonicalInstance = value;
        }
    }

    /// <summary>
    /// Logic to run after the monster is first added to the combat room. No-op by default.
    /// </summary>
    public virtual Task AfterAddedToRoom() => Task.CompletedTask;

    /// <summary>
    /// Logic to run after the monster is removed from the combat room. No-op by default.
    /// </summary>
    public virtual void BeforeRemovedFromRoom() { }

    public static LocString L10NMonsterLookup(string entryName)
    {
        return new LocString(_locTableName, entryName);
    }

    public MonsterModel ToMutable()
    {
        AssertCanonical();
        MonsterModel clone = (MonsterModel)MutableClone();
        clone.CanonicalInstance = this;
        return clone;
    }

    protected abstract MonsterMoveStateMachine GenerateMoveStateMachine();

    public void SetUpForCombat()
    {
        MoveStateMachine = GenerateMoveStateMachine();
        SpawnedThisTurn = true;
    }

    public void RollMove(IEnumerable<Creature> targets)
    {
        NextMove = MoveStateMachine!.RollMove(targets, Creature, ClimbRng.MonsterAi);
    }

    public void SetMoveImmediate(MoveState state, bool forceTransition = false)
    {
        // Some move transitions need to take precedence over the current move hasn't happened yet,
        // ie: Test Subject should transition for its revival even if its been stunned
        if (!NextMove.CanTransitionAway && !forceTransition) return;

        NextMove = state;
        MoveStateMachine!.ForceCurrentState(state);

        NCreature? creatureNode = NCombatRoom.Instance?.GetCreatureNode(Creature);

        if (creatureNode != null)
        {
            TaskHelper.RunSafely(creatureNode.RefreshIntents());
        }
    }

    public async Task PerformMove()
    {
        await Cmd.CustomScaledWait(0.1f, 0.2f, 0.25f);
        IsPerformingMove = true;

        // Store CombatState before performing move, in case the creature dies and is removed from combat during its
        // move (due to Thorns or something).
        CombatState combatState = CombatState;

        MoveState move = NextMove;
        IReadOnlyList<Creature> targets = combatState.PlayerCreatures;

        Log.Info($"Monster {Id.Entry} performing move {move.Id}");

        await move.PerformMove(targets);
        MoveStateMachine!.OnMovePerformed(move);

        CombatManager.Instance.History.MonsterPerformedMove(this, move, targets, combatState);

        IsPerformingMove = false;

        if (Creature.IsDead && Hook.ShouldCreatureBeRemovedFromCombatAfterDeath(CombatState, Creature))
        {
            combatState.RemoveCreature(Creature);
        }

        await Cmd.CustomScaledWait(0.1f, 0.4f, 0.5f);
    }

    public virtual void SetupSkins(NCreatureVisuals visuals) { }

    public virtual CreatureAnimator GenerateAnimator(MegaSprite controller)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState castAnim = new(AnimState.castAnim);
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        castAnim.NextState = idleAnim;
        attackAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;

        CreatureAnimator animator = new(idleAnim, controller);

        animator.AddAnyState(CreatureAnimator.idleTrigger, idleAnim);
        animator.AddAnyState(CreatureAnimator.castTrigger, castAnim);
        animator.AddAnyState(CreatureAnimator.attackTrigger, attackAnim);
        animator.AddAnyState(CreatureAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(CreatureAnimator.hitTrigger, hurtAnim);

        return animator;
    }

    public void OnSideSwitch()
    {
        AssertMutable();
        SpawnedThisTurn = false;
    }

    /// <summary>
    /// Called when this monster is about to die to Doom.
    /// Primarily used set up the creature visuals for the Doom vfx (ie hiding nodes that have additive materials)
    /// </summary>
    public virtual void OnDieToDoom() { }
}
