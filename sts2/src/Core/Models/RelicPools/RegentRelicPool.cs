using System.Collections.Generic;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Models.RelicPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class RegentRelicPool : RelicPoolModel
{
    public override string EnergyColorName => Regent.energyColorName;

    protected override IEnumerable<RelicModel> GenerateAllRelics() =>
    [
        ModelDb.Relic<FencingManual>(),
        ModelDb.Relic<LunarPastry>(),
        ModelDb.Relic<MiniRegent>(),
        ModelDb.Relic<MyMantle>(),
        ModelDb.Relic<OrangeDough>(),
        ModelDb.Relic<Regalite>(),
        ModelDb.Relic<TheThrone>(),
        ModelDb.Relic<VitruvianMinion>()
    ];
}
