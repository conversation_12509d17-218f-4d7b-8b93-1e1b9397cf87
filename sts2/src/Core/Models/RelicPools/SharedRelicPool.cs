using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Timeline.Epochs;
using MegaCrit.Sts2.Core.Unlocks;

namespace MegaCrit.Sts2.Core.Models.RelicPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SharedRelicPool : RelicPoolModel
{
    public override string EnergyColorName => ColorlessCardPool.energyColorName;

    protected override IEnumerable<RelicModel> GenerateAllRelics() =>
    [
        ModelDb.Relic<AmethystAubergine>(),
        ModelDb.Relic<Anchor>(),
        ModelDb.Relic<ArtOfWar>(),
        ModelDb.Relic<BagOfMarbles>(),
        ModelDb.Relic<BagOfPreparation>(),
        ModelDb.Relic<BeatingRemnant>(),
        ModelDb.Relic<Bellows>(),
        ModelDb.Relic<BeltBuckle>(),
        ModelDb.Relic<BentNail>(),
        ModelDb.Relic<BloodVial>(),
        ModelDb.Relic<BookOfFiveRings>(),
        ModelDb.Relic<Bread>(),
        ModelDb.Relic<BurningSticks>(),
        ModelDb.Relic<CaptainsWheel>(),
        ModelDb.Relic<Cauldron>(),
        ModelDb.Relic<CentennialPuzzle>(),
        ModelDb.Relic<ChainLinks>(),
        ModelDb.Relic<DingyRug>(),
        ModelDb.Relic<DollysMirror>(),
        ModelDb.Relic<DragonFruit>(),
        ModelDb.Relic<DybbukCube>(),
        ModelDb.Relic<EternalFeather>(),
        ModelDb.Relic<ForeverIce>(),
        ModelDb.Relic<FrozenEgg>(),
        ModelDb.Relic<FyshingFloat>(),
        ModelDb.Relic<GamblingChip>(),
        ModelDb.Relic<GamePiece>(),
        ModelDb.Relic<GhostSeed>(),
        ModelDb.Relic<Girya>(),
        ModelDb.Relic<GnarledHammer>(),
        ModelDb.Relic<GremlinHorn>(),
        ModelDb.Relic<HappyFlower>(),
        ModelDb.Relic<HornCleat>(),
        ModelDb.Relic<HungeringPortrait>(),
        ModelDb.Relic<IceCream>(),
        ModelDb.Relic<InkBottle>(),
        ModelDb.Relic<IntimidatingHelmet>(),
        ModelDb.Relic<JossPaper>(),
        ModelDb.Relic<JuzuBracelet>(),
        ModelDb.Relic<Kifuda>(),
        ModelDb.Relic<Kunai>(),
        ModelDb.Relic<Kusarigama>(),
        ModelDb.Relic<Lantern>(),
        ModelDb.Relic<LeesWaffle>(),
        ModelDb.Relic<LetterOpener>(),
        ModelDb.Relic<LightBulb>(),
        ModelDb.Relic<LizardTail>(),
        ModelDb.Relic<LoomingFruit>(),
        ModelDb.Relic<LuckyFysh>(),
        ModelDb.Relic<Mango>(),
        ModelDb.Relic<Matchbox>(),
        ModelDb.Relic<MealTicket>(),
        ModelDb.Relic<MeatOnTheBone>(),
        ModelDb.Relic<MembershipCard>(),
        ModelDb.Relic<MercuryHourglass>(),
        ModelDb.Relic<MoltenEgg>(),
        ModelDb.Relic<MummifiedHand>(),
        ModelDb.Relic<MysticLighter>(),
        ModelDb.Relic<Nunchaku>(),
        ModelDb.Relic<NyeShroud>(),
        ModelDb.Relic<OddlySmoothStone>(),
        ModelDb.Relic<OldCoin>(),
        ModelDb.Relic<Orichalcum>(),
        ModelDb.Relic<OrnamentalFan>(),
        ModelDb.Relic<Orrery>(),
        ModelDb.Relic<Pantograph>(),
        ModelDb.Relic<ParryingShield>(),
        ModelDb.Relic<Pear>(),
        ModelDb.Relic<PenNib>(),
        ModelDb.Relic<Pendulum>(),
        ModelDb.Relic<PetrifiedToad>(),
        ModelDb.Relic<Planisphere>(),
        ModelDb.Relic<Pocketwatch>(),
        ModelDb.Relic<PotionBelt>(),
        ModelDb.Relic<PrayerWheel>(),
        ModelDb.Relic<PunchDagger>(),
        ModelDb.Relic<RainbowRing>(),
        ModelDb.Relic<RedMask>(),
        ModelDb.Relic<RegalPillow>(),
        ModelDb.Relic<ReptileTrinket>(),
        ModelDb.Relic<RingingTriangle>(),
        ModelDb.Relic<RippleBasin>(),
        ModelDb.Relic<RoyalStamp>(),
        ModelDb.Relic<ScreamingFlagon>(),
        ModelDb.Relic<Shovel>(),
        ModelDb.Relic<Shuriken>(),
        ModelDb.Relic<SlingOfCourage>(),
        ModelDb.Relic<StarterFlare>(),
        ModelDb.Relic<StoneCalendar>(),
        ModelDb.Relic<StoneCracker>(),
        ModelDb.Relic<Strawberry>(),
        ModelDb.Relic<StrikeDummy>(),
        ModelDb.Relic<TheAbacus>(),
        ModelDb.Relic<TheCourier>(),
        ModelDb.Relic<TinyMailbox>(),
        ModelDb.Relic<Toolbox>(),
        ModelDb.Relic<ToxicEgg>(),
        ModelDb.Relic<TungstenRod>(),
        ModelDb.Relic<TuningFork>(),
        ModelDb.Relic<UnceasingTop>(),
        ModelDb.Relic<Vajra>(),
        ModelDb.Relic<Vambrace>(),
        ModelDb.Relic<VenerableTeaSet>(),
        ModelDb.Relic<WarPaint>(),
        ModelDb.Relic<Whetstone>(),
        ModelDb.Relic<WhiteBeastStatue>(),
        ModelDb.Relic<WhiteStar>()
    ];

    public override IEnumerable<RelicModel> GetUnlockedRelics(UnlockState unlockState)
    {
        List<RelicModel> unlockedRelics = AllRelics.ToList();

        if (!unlockState.IsEpochRevealed<Relic1Epoch>())
        {
            unlockedRelics.RemoveAll(r => Relic1Epoch.Relics.Any(relic => relic.Id == r.Id));
        }

        if (!unlockState.IsEpochRevealed<Relic2Epoch>())
        {
            unlockedRelics.RemoveAll(r => Relic2Epoch.Relics.Any(relic => relic.Id == r.Id));
        }

        if (!unlockState.IsEpochRevealed<Relic3Epoch>())
        {
            unlockedRelics.RemoveAll(r => Relic3Epoch.Relics.Any(relic => relic.Id == r.Id));
        }

        if (!unlockState.IsEpochRevealed<Relic4Epoch>())
        {
            unlockedRelics.RemoveAll(r => Relic4Epoch.Relics.Any(relic => relic.Id == r.Id));
        }

        if (!unlockState.IsEpochRevealed<Relic5Epoch>())
        {
            unlockedRelics.RemoveAll(r => Relic5Epoch.Relics.Any(relic => relic.Id == r.Id));
        }

        return unlockedRelics;
    }
}
