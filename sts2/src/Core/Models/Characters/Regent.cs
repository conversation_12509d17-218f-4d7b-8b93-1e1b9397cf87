using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.PotionPools;
using MegaCrit.Sts2.Core.Models.RelicPools;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Models.Characters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Regent : CharacterModel
{
    public const string energyColorName = "regent";
    public override Color NameColor => StsColors.orange;
    public override int StartingHp => 75;
    public override int StartingGold => 99;

    public override bool ShouldAlwaysShowStarCounter => true;

    public override CardPoolModel CardPool => ModelDb.CardPool<RegentCardPool>();
    public override RelicPoolModel RelicPool => ModelDb.RelicPool<RegentRelicPool>();
    public override PotionPoolModel PotionPool => ModelDb.PotionPool<RegentPotionPool>();

    public override IEnumerable<CardModel> StartingDeck =>
    [
        ModelDb.Card<StrikeRegent>(),
        ModelDb.Card<StrikeRegent>(),
        ModelDb.Card<StrikeRegent>(),
        ModelDb.Card<StrikeRegent>(),
        ModelDb.Card<DefendRegent>(),
        ModelDb.Card<DefendRegent>(),
        ModelDb.Card<DefendRegent>(),
        ModelDb.Card<DefendRegent>(),
        ModelDb.Card<FallingStar>(),
        ModelDb.Card<Venerate>()
    ];

    public override IEnumerable<IReadOnlyList<CardModel>> CardBundles =>
    [
        [ModelDb.Card<CollisionCourse>(), ModelDb.Card<KnowThyPlace>(), ModelDb.Card<Supermassive>()],
        [ModelDb.Card<Glow>(), ModelDb.Card<CloakOfStars>(), ModelDb.Card<KnockoutBlow>()],
        [ModelDb.Card<CosmicIndifference>(), ModelDb.Card<PhotonCut>(), ModelDb.Card<KinglyPunch>()],
        [ModelDb.Card<SolarStrike>(), ModelDb.Card<GatherLight>(), ModelDb.Card<Quasar>()],
        [ModelDb.Card<GravityWell>(), ModelDb.Card<LunarBlast>(), ModelDb.Card<Monologue>()],
        [ModelDb.Card<WroughtInWar>(), ModelDb.Card<RefineBlade>(), ModelDb.Card<SummonForth>()]
    ];

    public override IReadOnlyList<RelicModel> StartingRelics => [ModelDb.Relic<TheThrone>()];

    public override float AttackAnimDelay => 0.15f;
    public override float CastAnimDelay => 0.25f;
    public override Color EnergyLabelOutlineColor => new("784000FF");
    public override string CharacterSelectSfx => "regent_intro.wav";

    public override Color MapDrawingColor => new("935206");

    public override Color RemoteTargetingLineColor => new("BFA270FF");
    public override Color RemoteTargetingLineOutline => new("784000FF");
}
