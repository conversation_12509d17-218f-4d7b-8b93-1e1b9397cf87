using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class NoEscape : CardModel
{
    private const string _baseDoomKey = "BaseDoom";
    private const string _extraDoomKey = "ExtraDoom";
    private const string _doomThresholdKey = "DoomThreshold";

    protected override int CanonicalEnergyCost => 1;

    public override CardType Type => CardType.Skill;
    public override CardRarity Rarity => CardRarity.Uncommon;

    public override TargetType TargetType => TargetType.AnyEnemy;


    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_baseDoom<PERSON><PERSON>, 10),
        new(_extraDoom<PERSON>ey, 5),
        new(_doomThreshold<PERSON>ey, 10),

        new PowerVar<Doom>(0)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Doom>()];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        RecalculateValues(target);
        await PowerCmd.Apply<Doom>(target, DynamicVars.Doom.BaseValue, Owner.Creature, this);
    }

    public override void RecalculateValues()
    {
        DynamicVars.Doom.BaseValue = DynamicVars[_baseDoomKey].BaseValue;
    }

    public override void RecalculateValues(Creature target)
    {
        // 5 extra doom for every 10 Doom already on this enemy
        decimal extraDoom = Math.Floor(target.GetPowerAmount<Doom>() / DynamicVars[_doomThresholdKey].BaseValue) * DynamicVars[_extraDoomKey].BaseValue;
        DynamicVars.Doom.BaseValue = DynamicVars[_baseDoomKey].BaseValue + extraDoom;
    }


    protected override void OnUpgrade()
    {
        DynamicVars[_baseDoomKey].UpgradeValueBy(5);
    }
}
