using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DeathMarch : CardModel
{
    private const string _baseDamageKey = "BaseDamage";
    private const string _soulsDamageKey = "SoulDamage";

    protected override int CanonicalEnergyCost => 1;
    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Uncommon;

    public override TargetType TargetType => TargetType.AnyEnemy;

    protected override bool ShouldGlowGoldInternal => SoulPlayedThisTurn;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromCard<Soul>()];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(_baseDamageKey, 8, DamageProps.card),
        new DamageVar(_soulsDamageKey, 24, DamageProps.card)
    ];

    private bool SoulPlayedThisTurn => CombatManager.Instance.History.CardPlaysFinished.Any(e =>
        e.Card is Soul &&
        e.HappenedThisTurn(CombatState) &&
        e.Card.Owner == Owner);

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        decimal damage = SoulPlayedThisTurn ? DynamicVars[_soulsDamageKey].BaseValue : DynamicVars[_baseDamageKey].BaseValue;

        await DamageCmd.Attack(damage)
            .FromCard(this)
            .Targeting(target)
            .WithHitFx(VfxCmd.slashPath)
            .Execute(choiceContext);
    }

    protected override void OnUpgrade()
    {
        DynamicVars[_baseDamageKey].UpgradeValueBy(2);
        DynamicVars[_soulsDamageKey].UpgradeValueBy(6);
    }
}
