using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Scrape : CardModel
{
    protected override int CanonicalEnergyCost => 1;
    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Uncommon;

    public override TargetType TargetType => TargetType.AnyEnemy;


    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(7, DamageProps.card),
        new CardsVar(4)
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        await DamageCmd.Attack(DynamicVars.Damage.BaseValue)
            .FromCard(this)
            .Targeting(target)
            .WithHitFx(VfxCmd.slashPath)
            .Execute(choiceContext);

        IEnumerable<CardModel> drawnCards = await CardPileCmd.Draw(choiceContext, DynamicVars.Cards.IntValue, Owner);
        IEnumerable<CardModel> nonZeroCostCards = drawnCards.Where(c =>
            c.EnergyCost.GetWithModifiers(CostModifiers.All) != 0 ||
            c.EnergyCost.CostsX ||
            c.CurrentStarCost > 0 ||
            c.HasStarCostX
        );

        await CardCmd.Discard(choiceContext, nonZeroCostCards);
    }

    protected override void OnUpgrade()
    {
        DynamicVars.Damage.UpgradeValueBy(3);
        DynamicVars.Cards.UpgradeValueBy(1);
    }
}
