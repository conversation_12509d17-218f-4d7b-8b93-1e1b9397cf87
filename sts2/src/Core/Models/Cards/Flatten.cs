using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Combat.History.Entries;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Flatten : CardModel
{
    private const string _baseDamageKey = "BaseDamage";
    private const string _extraDamageKey = "ExtraDamage";

    protected override int CanonicalEnergyCost => 2;

    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Uncommon;

    public override TargetType TargetType => TargetType.AnyEnemy;

    protected override bool ShouldGlowRedInternal => Owner.IsOstyMissing;
    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.Static(StaticHoverTip.SummonStatic)];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_baseDamageKey, 5),
        new(_extraDamageKey, 2),
        new OstyDamageVar(15, DamageProps.card)
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        if (Osty.CheckMissingWithAnim(Owner)) return;

        RecalculateValues();

        await DamageCmd.Attack(DynamicVars.OstyDamage.BaseValue)
            .FromOsty(Owner.Osty!, this)
            .Targeting(target)
            .WithHitFx(VfxCmd.bluntPath, tmpSfx: TmpSfx.bluntAttack)
            .Execute(choiceContext);
    }

    public override void RecalculateValues()
    {
        int timesSummonedThisTurn = CombatManager.Instance.History.Entries.OfType<SummonedEntry>().Count(e =>
            e.Actor == Owner.Creature
        );

        DynamicVars.OstyDamage.BaseValue =
            DynamicVars[_baseDamageKey].BaseValue +
            DynamicVars[_extraDamageKey].BaseValue * timesSummonedThisTurn;
    }

    protected override void OnUpgrade()
    {
        DynamicVars[_extraDamageKey].UpgradeValueBy(1);
    }
}
