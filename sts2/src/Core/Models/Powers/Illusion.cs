using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Illusion : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Single;
    public override bool ShouldPlayVfx => false;

    private string? _followUpStateId;

    public string? FollowUpStateId
    {
        get => _followUpStateId;
        set
        {
            AssertMutable();
            _followUpStateId = value;
        }
    }

    private class Data
    {
        public bool isReviving;
    }

    protected override object InitInternalData() => new Data();

    private bool IsReviving => GetInternalData<Data>().isReviving;

    /// <summary>
    /// Illusions keep their buffs (including Illusion itself) after dying.
    /// </summary>
    public override bool ShouldPowerBeRemovedOnDeath(PowerModel power) => power.Type == PowerType.Debuff;

    public override Task AfterApplied()
    {
        if (Owner.HasPower<Minion>()) return Task.CompletedTask;

        return PowerCmd.Apply<Minion>(Owner, 1, null, null);
    }

    public override Task AfterDeath(PlayerChoiceContext choiceContext, Creature creature, bool wasRemovalPrevented, float deathAnimLength)
    {
        if (wasRemovalPrevented) return Task.CompletedTask;
        if (creature != Owner) return Task.CompletedTask;

        GetInternalData<Data>().isReviving = true;

        MoveState reviveMove = new("REVIVE_MOVE", ReviveMove, new HealIntent())
        {
            FollowUpStateId = FollowUpStateId ?? Owner.Monster!.MoveStateMachine!.StateLog.Last().Id,
            MustPerformOnceBeforeTransitioning = true
        };

        Owner.Monster!.SetMoveImmediate(reviveMove);

        return Task.CompletedTask;
    }

    /// <summary>
    /// This is so the owner doesn't receive powers while it is reviving.
    /// </summary>
    public override bool ShouldAllowHitting(Creature creature)
    {
        if (creature != Owner) return true;
        if (IsReviving) return false;

        return true;
    }

    public override bool ShouldCreatureBeRemovedFromCombatAfterDeath(Creature creature)
    {
        if (creature != Owner) return true;
        return false;
    }

    private async Task ReviveMove(IReadOnlyList<Creature> targets)
    {
        GetInternalData<Data>().isReviving = false;
        await CreatureCmd.Heal(Owner, Owner.MaxHp - Owner.CurrentHp);
    }
}
