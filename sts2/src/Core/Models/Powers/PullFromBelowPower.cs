using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class PullFromBelowPower : PowerModel
{
    public override PowerType Type => PowerType.Debuff;
    public override PowerStackType StackType => PowerStackType.Counter;
    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromKeyword(CardKeyword.Ethereal)];

    private class Data
    {
        /// <summary>
        /// Keep track of the cards we've seen played and the power amount at the time they were played.
        /// This lets Pull From Below avoid triggering on cards that started play before it was applied, and avoid dealing
        /// extra damage on multiple plays of Pull From Below that may have become ethereal.
        /// </summary>
        public readonly Dictionary<CardModel, int> amountsForPlayedCards = [];
    }

    protected override object InitInternalData() => new Data();

    public override Task BeforeCardPlayed(CardModel card, Creature? _, int playCount, PileType resultPile)
    {
        if (card.Owner.Creature != Applier) return Task.CompletedTask;
        if(!card.Keywords.Contains(CardKeyword.Ethereal)) return Task.CompletedTask;

        GetInternalData<Data>().amountsForPlayedCards.Add(card, Amount);
        return Task.CompletedTask;
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card.Owner.Creature != Applier) return;
        if (!GetInternalData<Data>().amountsForPlayedCards.Remove(card, out int amount)) return;

        await Cmd.CustomScaledWait(0.2f, 0.4f);
        Flash();
        await CreatureCmd.Damage(context, Owner, amount, DamageProps.nonCardHpLoss, null, null);
    }
}
