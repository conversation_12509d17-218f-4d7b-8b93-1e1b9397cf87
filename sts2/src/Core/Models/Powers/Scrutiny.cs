using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models.Afflictions;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Scrutiny : PowerModel
{
    public override PowerType Type => PowerType.Debuff;
    public override PowerStackType StackType => PowerStackType.Counter;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => HoverTipFactory.FromAffliction<Scrutinized>(Amount).Concat([HoverTipFactory.FromKeyword(CardKeyword.Exhaust)]);

    public override async Task AfterApplied()
    {
        AfflictionModel affliction = ModelDb.Affliction<Scrutinized>();

        foreach (CardModel card in Owner.Player!.PlayerCombatState!.AllCards)
        {
            if (affliction.CanAfflict(card))
            {
                await CardCmd.Afflict<Scrutinized>(card, Amount);
            }
        }
    }

    public override async Task AfterCardEnteredCombat(CardModel card)
    {
        if (card.Owner != Owner.Player) return;
        if (card.Affliction != null) return;

        await CardCmd.Afflict<Scrutinized>(card, Amount);
    }

    public override async Task AfterTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Side) return;

        await PowerCmd.Remove(this);
    }

    public override Task AfterRemoved(Creature oldOwner)
    {
        foreach (CardModel card in Owner.Player!.PlayerCombatState!.AllCards)
        {
            if (card.Affliction is Scrutinized)
            {
                CardCmd.ClearAffliction(card);
            }
        }

        return Task.CompletedTask;
    }
}
