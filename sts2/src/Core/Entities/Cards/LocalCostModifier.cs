using System;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.Entities.Cards;

/// <summary>
/// A class representing a single local modifier to a card's cost.
///
/// A <see cref="CardModel"/>'s <see cref="CardEnergyCost"/> contains a list of these local modifiers, which are
/// executed in the order they were applied to determine the card's final cost (before global modifiers if requested).
///
/// For example, imagine the following scenario:
///
/// You have a card with a base cost of 5. The following sequence of events occurs:
/// 1. An effect sets the card's cost to 2 (absolute) for the whole combat.
/// 2. Another effect increases the card's cost by 1 (relative) for the whole combat.
/// 3. Another effect sets the card's cost to 0 (absolute) just for this turn.
/// 4. Another effect increases the card's cost by 1 (relative) for the whole combat.
///
/// The CardEnergyCost's list of local modifiers would look like this:
///
/// [
///     LocalCostModifier(2, Absolute, WholeCombat),
///     LocalCostModifier(1, Relative, WholeCombat),
///     LocalCostModifier(0, Absolute, ThisTurn),
///     LocalCostModifier(1, Relative, WholeCombat)
/// ]
///
/// And the card's calculated cost would be 1.
///
/// Then, after the turn ends, the LocalCostModifier(0, Absolute, ThisTurn) modifier would wear off, and the list of
/// local modifiers would look like this:
///
/// [
///     LocalCostModifier(2, Absolute, WholeCombat),
///     LocalCostModifier(1, Relative, WholeCombat),
///     LocalCostModifier(1, Relative, WholeCombat)
/// ]
///
/// And the card's calculated cost would be 4.
/// </summary>
public class LocalCostModifier
{
    /// <summary>
    /// This modifier's amount.
    /// For <see cref="LocalCostType.Absolute"/>, this is the cost that the card should be set to.
    /// For <see cref="LocalCostType.Relative"/>, this is the amount that the card's cost should be offset by.
    /// </summary>
    public int Amount { get; set; }

    /// <summary>
    /// What algorithm should be used to apply this modifier to its card's cost.
    /// </summary>
    public LocalCostType Type { get; }

    /// <summary>
    /// How long this modifier should last for. Depending on this value, the modifier may wear off after certain game
    /// events. For example, a modifier with <see cref="LocalCostDuration.UntilPlayed"/> will wear off after the card
    /// is played.
    /// </summary>
    public LocalCostDuration Duration { get; }

    public LocalCostModifier(int amount, LocalCostType type, LocalCostDuration duration)
    {
        Amount = amount;
        Type = type;
        Duration = duration;
    }

    /// <summary>
    /// Modify the passed cost.
    /// </summary>
    public int Modify(int currentCost)
    {
        return Type switch
        {
            LocalCostType.Absolute => Amount,
            LocalCostType.Relative => currentCost + Amount,
            _ => throw new ArgumentOutOfRangeException(nameof(Type), Type, null)
        };
    }
}
