using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;

namespace MegaCrit.Sts2.Core.Entities.Cards;

/// <summary>
/// Card Keywords are extra "automatic behaviors" you can add to a card, which also automatically add some extra
/// text to the card's description.
///
/// Examples of things that SHOULD be CardKeywords:
/// * Exhaust: automatically exhausts when played, puts "Exhaust" in the card description.
/// * Unplayable: blocks the card from being played, puts "Unplayable" in the card description.
///
/// Examples of things that should NOT be CardKeywords:
/// * Strike: this has no logic or extra text, just tells Perfected Strike (and some other models) that it's a
///     Strike card.
/// * Block: while this is used on cards that have "Block" in their descriptions, it doesn't automatically add
///     it, nor does it automatically grant block; it's just for HoverTips and effects that care about which cards
///     give block (like the Nimble enchantment, which should only apply to cards that give block).
/// </summary>
public enum CardKeyword
{
    None = 0,
    Exhaust = 1,
    Ethereal = 2,
    Innate = 3,
    Unplayable = 4,
    Retain = 5,
    Sly = 6,
    Eternal = 7,
}

internal static class CardKeywordExtensions
{
    public static string GetLocKeyPrefix(this CardKeyword keyword) => StringHelper.Slugify(keyword.ToString());

    public static LocString GetTitle(this CardKeyword keyword)
    {
        return new LocString("card_keywords", $"{GetLocKeyPrefix(keyword)}.title");
    }

    public static LocString GetDescription(this CardKeyword keyword)
    {
        return new LocString("card_keywords", $"{GetLocKeyPrefix(keyword)}.description");
    }

    public static string GetCardText(this CardKeyword keyword)
    {
        return $"[gold]{keyword.GetTitle().GetFormattedText()}[/gold].";
    }
}
