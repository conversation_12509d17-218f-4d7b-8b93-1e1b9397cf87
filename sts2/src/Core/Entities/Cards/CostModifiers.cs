using System;
using MegaCrit.Sts2.Core.Models.Cards;

namespace MegaCrit.Sts2.Core.Entities.Cards;

/// <summary>
/// An enum representing the types of modifiers that should be included when calculating a card's energy cost.
/// </summary>
[Flags]
public enum CostModifiers
{
    /// <summary>
    /// No modifiers at all. This will just return the card's unmodified energy cost.
    ///
    /// What is the difference between this and <see cref="CardEnergyCost.Canonical"/>? A card's canonical cost is what
    /// would be "printed" on the card if it was printed out on paper, which means it cannot include _permanent_
    /// modifications. The most prolific permanent modification is an upgrade; cards like <see cref="Apotheosis"/>
    /// reduce their cost by 1 when upgraded. This change will be reflected when using <see cref="CostModifiers.None"/>,
    /// but not when calling <see cref="CardEnergyCost.Canonical"/>.
    /// </summary>
    None = 0,

    /// <summary>
    /// Include any modifiers that have been applied directly to the card.
    /// These modifiers live locally on the card itself, and will persist regardless of changes to other models in the
    /// combat state.
    /// </summary>
    /// <example>
    /// See <see cref="LocalCostDuration"/> for examples.
    /// </example>
    Local = 1 << 1,

    /// <summary>
    /// Include any modifiers that are persistently applied by other models in the global game state, and that may
    /// change or wear off based on changes to other models in the game state.
    /// </summary>
    /// <example>
    /// <see cref="Enthralled"/>, which applies a persistent "cost 1 more energy" modifier to all cards in the player's
    /// hand. This effect will wear off if Enthralled leaves the player's hand.
    /// </example>
    Global = 1 << 2,

    /// <summary>
    /// Include all modifiers.
    /// </summary>
    All = ~0
}
