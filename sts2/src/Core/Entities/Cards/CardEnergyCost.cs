using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Entities.Cards;

/// <summary>
/// Encapsulates all energy cost related properties and methods for a CardModel.
/// This class handles base energy costs, local and global modifications, and energy cost calculations.
/// </summary>
public sealed class CardEnergyCost
{
    private readonly CardModel _card;
    private int _base;
    private int _capturedXValue;

    /// <summary>
    /// This card's local cost modifiers.
    /// See <see cref="LocalCostModifier"/> for details on how this works.
    /// </summary>
    private List<LocalCostModifier> _localModifiers = [];

    /// <summary>
    /// This card's "official" starting energy cost.
    /// This is what would appear on the card if it was printed out on paper.
    /// </summary>
    public int Canonical { get; }

    /// <summary>
    /// Whether this card has an energy cost of X.
    /// X-cost-cards automatically spend all of the player's remaining energy when played, and their effect is
    /// multiplied by the amount spent.
    /// </summary>
    public bool CostsX { get; }

    /// <summary>
    /// Was this card's energy cost just recently upgraded?
    /// This is mainly used to show upgrade preview values in green.
    /// This should be cleared after the upgrade is complete.
    /// </summary>
    public bool WasJustUpgraded { get; private set; }

    /// <summary>
    /// Does this energy cost have any local modifiers?
    /// See <see cref="CostModifiers.Local"/> for details.
    /// </summary>
    public bool HasLocalModifiers => _localModifiers.Count > 0;

    public CardEnergyCost(CardModel card, int canonicalCost, bool costsX)
    {
        _card = card;
        CostsX = costsX;
        Canonical = CostsX ? 0 : canonicalCost;
        _base = Canonical;
    }

    /// <summary>
    /// Get this card's energy cost, including the specified modifier types.
    /// See <see cref="CostModifiers"/> for details on what types are available.
    /// </summary>
    public int GetWithModifiers(CostModifiers modifiers)
    {
        int cost = _base;

        // Don't include any modifiers for canonical cards.
        if (_card.IsCanonical) return cost;

        // An X-cost (like Whirlwind) or a base energy cost below 0 (like an unplayable curse) indicates that the
        // card's energy cost is irrelevant, so we skip the rest of the logic.
        if (_base < 0) return cost;
        if (CostsX) return cost;

        // If requested, include cost modifiers that have been applied directly to the card.
        // See CostModifiers.Local for details.
        if (modifiers.HasFlag(CostModifiers.Local))
        {
            foreach (LocalCostModifier modifier in _localModifiers)
            {
                cost = modifier.Modify(cost);
            }
        }

        // If requested and we're in combat, include cost modifiers that are based on other
        // models in the combat state.
        if (modifiers.HasFlag(CostModifiers.Global) && _card.CombatState != null)
        {
            cost = (int)Hook.ModifyEnergyCostInCombat(_card.CombatState, _card, cost);
        }

        return Math.Max(0, cost);
    }

    /// <summary>
    /// The amount of energy most recently spent to play this X-cost card.
    /// Used when duplicating X-cost cards, to make sure the duplicates are played with the same value.
    /// </summary>
    public int CapturedXValue
    {
        get
        {
            if (!CostsX) throw new InvalidOperationException("Only X-cost cards have a captured value.");
            return _capturedXValue;
        }

        set
        {
            _card.AssertMutable();

            if (!CostsX) throw new InvalidOperationException("Only X-cost cards have a captured value.");

            _capturedXValue = value;
        }
    }

    /// <summary>
    /// Get the amount of energy that should be spent to play this card.
    ///
    /// * For X-cost cards, this is the amount of energy that its owner has.
    /// * For normal cards, this is the current cost including all modifiers
    ///   (see <see cref="GetWithModifiers"/> with <see cref="CostModifiers.All"/>) clamped to 0.
    ///
    /// The game uses this value when actually spending the energy to play the card.
    /// Additionally, this is useful for effects that need to know how much WOULD be spent to play the card without
    /// actually playing it, such as <see cref="Recycle"/>.
    /// </summary>
    public int GetAmountToSpend()
    {
        if (CostsX) return _card.Owner.PlayerCombatState?.Energy ?? 0;

        return Math.Max(0, GetWithModifiers(CostModifiers.All));
    }

    /// <summary>
    /// Get the "resolved" cost of this card. This can mean one of two things:
    ///
    /// * For X-cost cards, this is the captured X-cost value (see <see cref="CapturedXValue"/>).
    /// * For normal cards, this is the current cost including all modifiers
    ///   (see <see cref="GetWithModifiers"/> with <see cref="CostModifiers.All"/>) clamped to 0.
    ///
    /// This is useful for effects that need to know the card's cost AFTER it was played, such as
    /// <see cref="IntimidatingHelmet"/>. For normal cards, these effects just care about the card's current cost
    /// (including all modifiers). For X-cost cards, these effects care about the X-value that was set for the card when
    /// it was played.
    /// </summary>
    public int GetResolved()
    {
        if (CostsX) return CapturedXValue;

        return Math.Max(0, GetWithModifiers(CostModifiers.All));
    }

    public void SetUntilPlayed(int cost)
    {
        // This means it's unplayable or has alternate play conditions.
        if (cost == 0 && Canonical < 0) return;

        _localModifiers.Add(new LocalCostModifier(cost, LocalCostType.Absolute, LocalCostDuration.UntilPlayed));
    }

    public void SetThisTurn(int cost)
    {
        // This means it's unplayable or has alternate play conditions.
        if (cost == 0 && Canonical < 0) return;

        _localModifiers.Add(new LocalCostModifier(cost, LocalCostType.Absolute, LocalCostDuration.ThisTurn));
    }

    public void SetThisCombat(int cost)
    {
        // This means it's unplayable or has alternate play conditions.
        if (cost == 0 && Canonical < 0) return;

        _localModifiers.Add(new LocalCostModifier(cost, LocalCostType.Absolute, LocalCostDuration.WholeCombat));
    }

    public void AddUntilPlayed(int amount)
    {
        // Skip if it's 0 (no change)
        if (amount == 0) return;

        _localModifiers.Add(new LocalCostModifier(amount, LocalCostType.Relative, LocalCostDuration.UntilPlayed));
    }

    public void AddThisTurn(int amount)
    {
        // Skip if it's 0 (no change)
        if (amount == 0) return;

        _localModifiers.Add(new LocalCostModifier(amount, LocalCostType.Relative, LocalCostDuration.ThisTurn));
    }

    public void AddThisCombat(int amount)
    {
        // Skip if it's 0 (no change)
        if (amount == 0) return;

        _localModifiers.Add(new LocalCostModifier(amount, LocalCostType.Relative, LocalCostDuration.WholeCombat));
    }

    /// <summary>
    /// Clear local cost modifiers that should last until the end of the turn.
    /// </summary>
    /// <returns>True if any modifiers were cleared and EnergyCostChanged should be invoked.</returns>
    public bool EndOfTurnCleanup()
    {
        _card.AssertMutable();
        return _localModifiers.RemoveAll(m => m.Duration == LocalCostDuration.ThisTurn) > 0;
    }

    /// <summary>
    /// Clear local cost modifiers that should last until the card is played.
    /// </summary>
    /// <returns>True if any modifiers were cleared and EnergyCostChanged should be invoked.</returns>
    public bool AfterCardPlayedCleanup()
    {
        _card.AssertMutable();

        // After a card is played, we remove any modifiers with the UntilPlayed duration as expected.
        // However, possibly unexpectedly, we also remove modifiers with the ThisTurn duration. This is for the unusual
        // case where a card is played, then returned to the hand, then played again on the same turn. In this case, we
        // want the modifier to wear off after the first play. This matches STS1 behavior, and helps avoid several
        // infinite combos that we don't want to enable.
        return _localModifiers.RemoveAll(m => m.Duration is LocalCostDuration.ThisTurn or LocalCostDuration.UntilPlayed) > 0;
    }

    /// <summary>
    /// Upgrade the energy cost of this card by the specified amount.
    /// This is meant to be called in <see cref="CardModel.OnUpgrade"/> and <see cref="EnchantmentModel.OnEnchant"/>.
    /// </summary>
    /// <param name="addend">Amount to add to the current cost (usually negative).</param>
    public void UpgradeBy(int addend)
    {
        _card.AssertMutable();

        if (CostsX) return;
        if (addend == 0) return;

        int oldBaseCost = _base;
        int newBaseCost = Math.Max(_base + addend, 0);

        WasJustUpgraded = true;

        // If upgrading a card reduces its cost, set all higher absolute local modifiers to the same cost, so the
        // upgrade still has a positive effect.
        // For example, Barricade costs 3, and its upgrade reduces it to 2. If an absolute LocalCostModifier sets
        // Barricade's cost to 5, and then the player plays Armaments and selects Barricade to upgrade, we want
        // Barricade+'s cost to be 2, not 4. We can't just remove the absolute modifier though, because that may cause
        // earlier relative modifiers to apply when they shouldn't, so instead we lower the absolute modifier to match
        // the new base cost.
        if (newBaseCost < oldBaseCost)
        {
            foreach (LocalCostModifier modifier in _localModifiers)
            {
                if (modifier.Type != LocalCostType.Absolute) continue;
                if (modifier.Amount <= newBaseCost) continue;

                modifier.Amount = newBaseCost;
            }
        }

        SetCustomBaseCost(newBaseCost);
    }

    /// <summary>
    /// Finalize an upgrade after calling UpgradeEnergyCostBy. This clears out state that is used for displaying an upgrade
    /// preview.
    /// </summary>
    public void FinalizeUpgrade()
    {
        _card.AssertMutable();
        WasJustUpgraded = false;
    }

    /// <summary>
    /// Reset energy cost to base values during downgrade.
    /// </summary>
    public void ResetForDowngrade()
    {
        _card.AssertMutable();
        _base = Canonical;
        _card.InvokeEnergyCostChanged();
    }

    /// <summary>
    /// BE VERY CAREFUL USING THIS!
    /// This is mainly meant for internal usage. The only external usage of this should be in <see cref="MadScience"/>.
    /// </summary>
    /// <param name="newBaseCost"></param>
    public void SetCustomBaseCost(int newBaseCost)
    {
        _card.AssertMutable();
        _base = newBaseCost;
        _card.InvokeEnergyCostChanged();
    }

    /// <summary>
    /// Create a deep clone of this EnergyCostInfo for the specified card.
    /// </summary>
    /// <param name="newCard">The card that will own the cloned EnergyCostInfo.</param>
    /// <returns>A deep clone of this EnergyCostInfo.</returns>
    public CardEnergyCost Clone(CardModel newCard)
    {
        CardEnergyCost clone = new(newCard, newCard.EnergyCost.Canonical, newCard.EnergyCost.CostsX)
        {
            _base = _base,
            _capturedXValue = _capturedXValue,
            WasJustUpgraded = WasJustUpgraded,
            _localModifiers = _localModifiers.ToList()
        };

        return clone;
    }
}
