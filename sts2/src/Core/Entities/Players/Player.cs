using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Potions;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Odds;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.Unlocks;

namespace MegaCrit.Sts2.Core.Entities.Players;

public class Player
{
    public const int initialMaxPotionSlotCount = 3;

    public event Action<RelicModel>? RelicObtained;
    public event Action<RelicModel>? RelicRemoved;
    public event Action<int>? MaxPotionCountChanged;
    public event Action<PotionModel>? PotionProcured;
    public event Action<PotionModel>? PotionDiscarded;
    public event Action<PotionModel>? UsedPotionRemoved;
    public event Action? AddPotionFailed;
    public event Action? GoldChanged;

    private CardPile[]? _climbPiles;
    private readonly List<RelicModel> _relics = [];
    private readonly List<PotionModel?> _potionSlots = [];

    // If more combat-relevant fields are added, remember to add them to NetFullCombatState
    public int MaxPotionCount => _potionSlots.Count;
    public CharacterModel Character { get; }
    public Creature Creature { get; }
    public ulong NetId { get; }

    /// <summary>
    /// Player-scoped RNG set.
    /// Note that this state is not deterministic outside of combat, events and rest sites
    /// This is initialized with a seed of 0, but is updated to a real seed when added to a <see cref="ClimbState"/>.
    /// </summary>
    public PlayerRngSet PlayerRng { get; private set; }

    /// <summary>
    /// Player-scoped odds set.
    /// Note that this state is not deterministic outside of combat, events and rest sites.
    /// This is initialized with a seed of 0, but is updated to a real seed when added to a <see cref="ClimbState"/>.
    /// </summary>
    public PlayerOddsSet PlayerOdds { get; private set; }

    /// <summary>
    /// Player-scoped relic grab bag.
    /// See <see cref="IClimbState.SharedRelicGrabBag"/> for the difference between this and the shared grab bag.
    /// Note that this state is not deterministic outside of combat, events and rest sites.
    /// </summary>
    public RelicGrabBag RelicGrabBag { get; }

    /// <summary>
    /// The set of unlocks the player entered the run with.
    /// This serves several purposes:
    /// - In multiplayer, this keeps track of each player's unlocks so that we generate only cards/potions/relics that
    ///   the player has unlocked.
    /// - In both singleplayer and multiplayer, unlocking epochs outside of the run does not change the unlocks in a
    ///   run that you load. The unlocks are saved into the climb save file.
    /// </summary>
    public UnlockState UnlockState { get; }

    private IClimbState _climbState = NullClimbState.Instance;

    /// <summary>
    /// The state of the climb that the player is in.
    /// Will usually be an instance of <see cref="ClimbState"/>, but will be <see cref="NullClimbState"/> in some spots
    /// for testing, debugging, or one-off combat scenarios. You should never have to check for this though, as
    /// <see cref="NullClimbState"/> should always behave appropriately.
    /// </summary>
    public IClimbState ClimbState
    {
        get => _climbState;
        set
        {
            if (_climbState is not NullClimbState) throw new InvalidOperationException("ClimbState has already been set.");
            _climbState = value;
        }
    }

    /// <summary>
    /// This is almost equivalent to Creature.IsAlive, except for a brief period of time between when the player's HP
    /// reaches zero and when DieInternal is called.
    /// It is used to allow hooks that prevent death to run before the player is fully considered dead.
    /// This is on Player and not Creature because monsters use a different mechanism for death prevention. Since
    /// creatures are always removed from combat after they are fully dead, their powers are always iterated, and we
    /// rely on creature removal to remove their powers from Hook. Players are different - they stick around after they
    /// are fully dead, and so we rely on this flag to stop model iteration.
    /// </summary>
    public bool IsActiveForHooks { get; private set; }

    public PlayerCombatState? PlayerCombatState { get; private set; }
    public ExtraPlayerFields ExtraFields { get; private set; } = new();

    public IReadOnlyList<RelicModel> Relics => _relics;
    public IReadOnlyList<PotionModel?> PotionSlots => _potionSlots;
    public IEnumerable<PotionModel> Potions => _potionSlots.Where(p => p != null).OfType<PotionModel>();

    /// <summary>
    /// Get this player's Osty pet Creature.
    /// If Osty is not in combat, this will return null.
    /// If Osty is dead, this will return the dead Osty Creature instance.
    ///
    /// Note: If you want to check that Osty is both present in combat and alive, use <see cref="IsOstyAlive"/>.
    /// If you want to check that Osty is missing from combat or dead, use <see cref="IsOstyMissing"/>.
    /// </summary>
    public Creature? Osty => PlayerCombatState?.GetPet<Osty>();

    /// <summary>
    /// Is Osty present in combat and alive?
    /// </summary>
    public bool IsOstyAlive => Osty is { IsAlive: true };

    /// <summary>
    /// Is Osty missing from combat or dead?
    /// </summary>
    public bool IsOstyMissing => !IsOstyAlive;

    private int _gold;

    public int Gold
    {
        get => _gold;
        set
        {
            if (value == Gold) return;

            _gold = value;
            GoldChanged?.Invoke();
        }
    }

    public bool CanAddPotion => _potionSlots.Any(p => p == null);

    public bool CanRemovePotions { get; set; } = true;

    /// <summary>
    /// Has this player's inventory been populated?
    /// A player object is initially created unpopulated, but the various populating methods should be called before
    /// the climb they're in has fully been launched.
    /// </summary>
    private bool IsInventoryPopulated => Deck.Cards.Any() || Relics.Any() || Potions.Any();

    #region Card Piles

    public CardPile Deck { get; } = new(PileType.Deck);

    #endregion

    #region Energy

    public int MaxEnergy { get; set; }

    #endregion

    #region Discoveries

    public uint DiscoveredCards { get; set; }
    public uint DiscoveredRelics { get; set; }
    public uint DiscoveredPotions { get; set; }
    public uint DiscoveredEnemies { get; set; }
    public uint DiscoveredEpochs { get; set; }

    #endregion

    public int BaseOrbSlotCount { get; set; }

    public IEnumerable<CardPile> Piles
    {
        get
        {
            _climbPiles ??= [Deck];

            return (PlayerCombatState?.AllPiles ?? []).Concat(_climbPiles);
        }
    }

    private Player(CharacterModel character, ulong netId, int currentHp, int maxHp, int maxEnergy, int gold, int potionSlotCount, int orbSlotCount, RelicGrabBag sharedRelicGrabBag, UnlockState unlockState, uint discoveredCards, uint discoveredEnemies, uint discoveredEpochs, uint discoveredPotions, uint discoveredRelics)
    {
        ClimbState = NullClimbState.Instance;
        Character = character;
        NetId = netId;

        Creature = new Creature(this, currentHp, maxHp);
        MaxEnergy = maxEnergy;
        Gold = gold;
        SetMaxPotionCountInternal(potionSlotCount);
        BaseOrbSlotCount = orbSlotCount;

        RelicGrabBag = sharedRelicGrabBag;
        UnlockState = unlockState;

        // Initialize with a seed of 0. These will be updated to a real seed when added to a ClimbState.
        PlayerRng = new PlayerRngSet(0);
        PlayerOdds = new PlayerOddsSet(PlayerRng);

        DiscoveredCards = discoveredCards;
        DiscoveredEnemies = discoveredEnemies;
        DiscoveredEpochs = discoveredEpochs;
        DiscoveredPotions = discoveredPotions;
        DiscoveredRelics = discoveredRelics;

        IsActiveForHooks = Creature.IsAlive;
    }

    /// <summary>
    /// Create a new player for use at the start of a new climb.
    /// The player's inventory will be populated with the chosen character's starting cards, relics, etc., but these
    /// models will not work properly until the player is added to a <see cref="ClimbState"/> and/or
    /// <see cref="CombatState"/>.
    /// </summary>
    /// <param name="unlockState">The set of unlocks the player entered the climb with.</param>
    /// <param name="netId">ID for uniquely identifying this player in multiplayer games.</param>
    /// <typeparam name="T">The type of the character that the player is playing as.</typeparam>
    /// <returns>A new player with an empty inventory.</returns>
    public static Player CreateForNewClimb<T>(UnlockState unlockState, ulong netId) where T : CharacterModel
    {
        return CreateForNewClimb(ModelDb.Character<T>(), unlockState, netId);
    }

    /// <summary>
    /// Create a new player for use at the start of a new climb.
    /// The player's inventory will be populated with the chosen character's starting cards, relics, etc., but these
    /// models will not work properly until the player is added to a <see cref="ClimbState"/> and/or
    /// <see cref="CombatState"/>.
    /// </summary>
    /// <param name="character">The character that the player is playing as.</param>
    /// <param name="unlockState">The set of unlocks the player entered the climb with.</param>
    /// <param name="netId">ID for uniquely identifying this player in multiplayer games.</param>
    /// <returns>A new player with an empty inventory.</returns>
    public static Player CreateForNewClimb(CharacterModel character, UnlockState unlockState, ulong netId)
    {
        Player player = new(
            character: character,
            netId: netId,
            currentHp: character.StartingHp,
            maxHp: character.StartingHp,
            maxEnergy: character.MaxEnergy,
            gold: character.StartingGold,
            potionSlotCount: initialMaxPotionSlotCount,
            orbSlotCount: character.BaseOrbSlotCount,
            sharedRelicGrabBag: new RelicGrabBag(),
            unlockState: unlockState,
            discoveredCards: 0,
            discoveredEnemies: 0,
            discoveredEpochs: 0,
            discoveredPotions: 0,
            discoveredRelics: 0
        );

        player.PopulateStartingInventory();

        return player;
    }

    /// <summary>
    /// Load a player from a SerializablePlayer.
    /// The player's inventory will be populated with the cards, relics, etc. from the SerializablePlayer., but these
    /// models will not work properly until the player is added to a <see cref="ClimbState"/> and/or
    /// <see cref="CombatState"/>.
    /// </summary>
    public static Player FromSerializable(SerializablePlayer save)
    {
        Player player = new(
            character: ModelDb.GetById<CharacterModel>(save.CharacterId!),
            netId: save.NetId,
            currentHp: save.CurrentHp,
            maxHp: save.MaxHp,
            maxEnergy: save.MaxEnergy,
            gold: save.Gold,
            potionSlotCount: save.MaxPotionSlotCount,
            orbSlotCount: save.BaseOrbSlotCount,
            sharedRelicGrabBag: RelicGrabBag.FromSerializable(save.RelicGrabBag),
            unlockState: UnlockState.FromSerializable(save.UnlockState),
            discoveredCards: save.DiscoveredCards,
            discoveredEnemies: save.DiscoveredEnemies,
            discoveredEpochs: save.DiscoveredEpochs,
            discoveredPotions: save.DiscoveredPotions,
            discoveredRelics: save.DiscoveredRelics
        );
        player.PlayerRng = PlayerRngSet.FromSerializable(save.Rng);
        player.PlayerOdds = PlayerOddsSet.FromSerializable(save.Odds, player.PlayerRng);

        player.ExtraFields = ExtraPlayerFields.FromSerializable(save.ExtraFields);
        player.LoadInventory(save);

        return player;
    }

    public void InitializeSeed(string seed)
    {
        // We mix in our NetId with the global seed to obtain a unique seed for our RNG set
        PlayerRng = new PlayerRngSet((uint)((ulong)StringHelper.GetDeterministicHashCode(seed) + NetId));
        PlayerOdds = new PlayerOddsSet(PlayerRng);
    }

    private void PopulateStartingInventory()
    {
        if (IsInventoryPopulated) throw new InvalidOperationException("Inventory is already populated.");
        if (ClimbState is not NullClimbState)
        {
            throw new InvalidOperationException(
                "A player's starting inventory must be populated before being added to a climb."
            );
        }

        PopulateStartingDeck();
        PopulateStartingRelics();

        foreach (PotionModel potion in Character.StartingPotions.Select(p => p.ToMutable()))
        {
            AddPotionInternal(potion);
        }
    }

    private void LoadInventory(SerializablePlayer save)
    {
        if (IsInventoryPopulated) throw new InvalidOperationException("Inventory is already populated.");
        if (ClimbState is not NullClimbState)
        {
            throw new InvalidOperationException("A player's inventory must be loaded before being added to a climb.");
        }

        // We use FromSerializable directly here instead of IClimbState.CreateCard, because this method is meant to be
        // called during player deserialization, before they've been added to a climb.
        // Once the player is added to a climb, this card will be added to it as well.
        PopulateDeck(save.Deck.Select(CardModel.FromSerializable), false);
        LoadPotions(save.Potions);
        PopulateRelics(save.Relics.Select(RelicModel.FromSerializable));
    }

    public void PopulateRelicGrabBagIfNecessary(Rng rng)
    {
        if (RelicGrabBag.IsPopulated) return;
        RelicGrabBag.Populate(this, rng);
    }

    public SerializablePlayer ToSerializable()
    {
        return new SerializablePlayer
        {
            CharacterId = Character.Id,
            CurrentHp = Creature.CurrentHp,
            MaxHp = Creature.MaxHp,
            MaxEnergy = MaxEnergy,
            MaxPotionSlotCount = MaxPotionCount,
            BaseOrbSlotCount = BaseOrbSlotCount,
            NetId = NetId,
            Gold = Gold,
            Rng = PlayerRng.ToSerializable(),
            Odds = PlayerOdds.ToSerializable(),
            RelicGrabBag = RelicGrabBag.ToSerializable(),
            Deck = Deck.Cards.Select(c => c.ToSerializable()).ToList(),
            Relics = Relics.Select(r => r.ToSerializable()).ToList(),
            Potions = PotionSlots.Select((p, i) => p?.ToSerializable(i)).OfType<SerializablePotion>().ToList(),
            ExtraFields = ExtraFields.ToSerializable(),
            UnlockState = UnlockState.ToSerializable(),
            DiscoveredCards = DiscoveredCards,
            DiscoveredEnemies = DiscoveredEnemies,
            DiscoveredEpochs = DiscoveredEpochs,
            DiscoveredPotions = DiscoveredPotions,
            DiscoveredRelics = DiscoveredRelics
        };
    }

    public void SyncWithSerializedPlayer(SerializablePlayer player)
    {
        if (player.NetId != NetId) throw new InvalidOperationException($"Tried to sync player that has net ID {NetId} with SerializablePlayer that has net ID {player.NetId}!");
        if (player.CharacterId != Character.Id) throw new InvalidOperationException($"Character changed for player {NetId}! This is not allowed");

        // Important to set max HP before current HP!
        Creature.SetMaxHpInternal(player.MaxHp);
        Creature.SetCurrentHpInternal(player.CurrentHp);
        MaxEnergy = player.MaxEnergy;
        Gold = player.Gold;
        SetMaxPotionCountInternal(player.MaxPotionSlotCount);

        Deck.Clear(true);

        foreach (RelicModel model in _relics.ToList())
        {
            RemoveRelicInternal(model, true);
        }

        foreach (PotionModel? model in _potionSlots.ToList())
        {
            if (model != null)
            {
                DiscardPotionInternal(model, true);
            }
        }

        PopulateDeck(player.Deck.Select(c => ClimbState.LoadCard(c, this)), true);
        PopulateRelics(player.Relics.Select(RelicModel.FromSerializable), true);
        LoadPotions(player.Potions, true);

        PlayerRng.LoadFromSerializable(player.Rng);
        PlayerOdds.LoadFromSerializable(player.Odds);
        RelicGrabBag.LoadFromSerializable(player.RelicGrabBag);

        DiscoveredCards = player.DiscoveredCards;
        DiscoveredEnemies = player.DiscoveredEnemies;
        DiscoveredEpochs = player.DiscoveredEpochs;
        DiscoveredPotions = player.DiscoveredPotions;
        DiscoveredRelics = player.DiscoveredRelics;

        IsActiveForHooks = Creature.IsAlive;
    }

    /// <summary>
    /// NEVER CALL THIS!
    /// Only RelicCmd.Obtain and save/load stuff should be calling this.
    /// </summary>
    /// <param name="relic">Relic to add.</param>
    /// <param name="index">Index at which relic should be inserted. -1 means at the end.</param>
    /// <param name="silent">If true, RelicObtained will not be called.</param>
    public void AddRelicInternal(RelicModel relic, int index = -1, bool silent = false)
    {
        relic.AssertMutable();
        relic.Owner = this;

        if (index == -1)
        {
            _relics.Add(relic);
        }
        else
        {
            _relics.Insert(index, relic);
        }

        if (relic is { IsMelted: false, ShouldFlashOnPlayer: true })
        {
            relic.Flashed += OnRelicFlashed;
        }

        if (!silent)
        {
            RelicObtained?.Invoke(relic);
        }
    }

    /// <summary>
    /// NEVER CALL THIS!
    /// ONLY RelicCmd.Remove should be calling this.
    /// </summary>
    /// <param name="relic">Relic to remove.</param>
    /// <param name="silent">If true, RelicRemoved will not be called.</param>
    public void RemoveRelicInternal(RelicModel relic, bool silent = false)
    {
        if (!_relics.Contains(relic)) throw new InvalidOperationException($"Player does not have relic {relic.Id}");

        _relics.Remove(relic);
        relic.RemoveInternal();

        if (relic.ShouldFlashOnPlayer)
        {
            relic.Flashed -= OnRelicFlashed;
        }

        if (!silent)
        {
            RelicRemoved?.Invoke(relic);
        }
    }

    /// <summary>
    /// NEVER CALL THIS!
    /// ONLY RelicCmd.Melt should be calling this.
    /// </summary>
    /// <param name="relic">Relic to melt.</param>
    public void MeltRelicInternal(RelicModel relic)
    {
        if (!relic.IsWax) throw new InvalidOperationException($"{relic.Id} is not wax.");
        if (relic.IsMelted) throw new InvalidOperationException($"{relic.Id} is already melted.");
        if (!_relics.Contains(relic)) throw new InvalidOperationException($"Player does not have relic {relic.Id}");

        if (relic.ShouldFlashOnPlayer)
        {
            relic.Flashed -= OnRelicFlashed;
        }

        relic.IsMelted = true;
        relic.Status = RelicStatus.Disabled;
    }

    /// <summary>
    /// Get one of this player's relics.
    /// If the player has multiple of the same type of relic, just get the first one.
    /// If the player has none of this type of relic, returns null.
    /// </summary>
    /// <typeparam name="T">Type of relic to get.</typeparam>
    /// <returns>Matching relic.</returns>
    public T? GetRelic<T>() where T : RelicModel => Relics.FirstOrDefault(r => r is T) as T;

    public RelicModel? GetRelicById(ModelId id) => Relics.FirstOrDefault(r => r.Id == id);

    /// <summary>
    /// Returns the slot index of the potion in the player's belt, or -1 if it is not in the belt.
    /// </summary>
    public int GetPotionSlotIndex(PotionModel model)
    {
        return _potionSlots.IndexOf(model);
    }

    /// <summary>
    /// Returns the potion at the slot index, or throws if the index is out of range.
    /// </summary>
    public PotionModel? GetPotionAtSlotIndex(int index)
    {
        if (index < 0 || index >= _potionSlots.Count)
            throw new IndexOutOfRangeException($"Index {index} is not a valid potion slot index! Player has {_potionSlots.Count} potion slots");

        return _potionSlots[index];
    }

    /// <summary>
    /// Increases the maximum amount of potions the player can hold.
    /// </summary>
    /// <param name="maxPotionCountIncrease">The increased count of maximum amount of potions the player can carry.</param>
    public void AddToMaxPotionCount(int maxPotionCountIncrease)
    {
        SetMaxPotionCountInternal(_potionSlots.Count + maxPotionCountIncrease);
    }

    /// <summary>
    /// Decreases the maximum amount of potions the player can hold.
    /// </summary>
    /// <param name="maxPotionCountDecrease">The decreased count of maximum amount of potions the player can carry.</param>
    public void SubtractFromMaxPotionCount(int maxPotionCountDecrease)
    {
        SetMaxPotionCountInternal(_potionSlots.Count - maxPotionCountDecrease);
    }

    /// <summary>
    /// NEVER CALL THIS!
    /// ONLY save/load stuff should be calling this.
    /// </summary>
    /// <param name="newMaxPotionCount">The new maximum amount of potions the player can carry.</param>
    private void SetMaxPotionCountInternal(int newMaxPotionCount)
    {
        if (newMaxPotionCount > _potionSlots.Count)
        {
            for (int i = _potionSlots.Count; i < newMaxPotionCount; i++)
            {
                _potionSlots.Add(null);
            }

            MaxPotionCountChanged?.Invoke(MaxPotionCount);
        }
        else if (newMaxPotionCount < _potionSlots.Count)
        {
            for (int i = _potionSlots.Count - 1; i >= newMaxPotionCount; i--)
            {
                // If there is a potion in the slot, check if it can be moved
                if (_potionSlots[i] != null)
                {
                    int emptyIndex = _potionSlots.IndexOf(null);

                    if (emptyIndex < newMaxPotionCount)
                    {
                        _potionSlots[emptyIndex] = _potionSlots[i];
                    }
                    else
                    {
                        DiscardPotionInternal(_potionSlots[i]!);
                    }
                }

                _potionSlots.RemoveAt(i);
            }

            MaxPotionCountChanged?.Invoke(MaxPotionCount);
        }
    }

    /// <summary>
    /// NEVER CALL THIS!
    /// ONLY PotionCmd.Procure and save/load stuff should be calling this.
    /// </summary>
    /// <param name="potion">Potion to add.</param>
    /// <param name="slotIndex">Slot at which to add the potion. If -1, the potion will be added in the first available slot.</param>
    /// <param name="silent">If true, no events will be called.</param>
    public PotionProcureResult AddPotionInternal(PotionModel potion, int slotIndex = -1, bool silent = false)
    {
        potion.AssertMutable();
        PotionProcureResult result = new()
        {
            potion = potion
        };

        if (slotIndex < 0)
        {
            slotIndex = _potionSlots.IndexOf(null);
        }

        if (slotIndex >= 0)
        {
            if (_potionSlots[slotIndex] != null)
            {
                Log.Warn($"Tried to add potion {potion} at slot index {slotIndex} which is already filled with potion {_potionSlots[slotIndex]}!");

                if (!silent)
                {
                    AddPotionFailed?.Invoke();
                }

                result.success = false;
                result.failureReason = PotionProcureFailureReason.TooFull;
                return result;
            }

            potion.Owner = this;

            _potionSlots[slotIndex] = potion;

            if (!silent)
            {
                PotionProcured?.Invoke(potion);
            }

            result.success = true;
        }
        else
        {
            if (!silent)
            {
                AddPotionFailed?.Invoke();
            }

            result.success = false;
            result.failureReason = PotionProcureFailureReason.TooFull;
        }

        return result;
    }

    /// <summary>
    /// NEVER CALL THIS!
    /// ONLY PotionModel.Discard should be calling this.
    /// </summary>
    /// <param name="potion">Potion to discard.</param>
    /// <param name="silent">If true, PotionDiscarded will not be called.</param>
    public void DiscardPotionInternal(PotionModel potion, bool silent = false)
    {
        int index = _potionSlots.IndexOf(potion);
        if (index < 0) throw new InvalidOperationException($"Tried to remove potion you don't have: {potion.Id}");

        _potionSlots[index] = null;

        if (!silent)
        {
            PotionDiscarded?.Invoke(potion);
        }
    }

    /// <summary>
    /// NEVER CALL THIS!
    /// ONLY PotionModel.Remove should be calling this.
    /// </summary>
    /// <param name="potion">Used potion to remove.</param>
    public void RemoveUsedPotionInternal(PotionModel potion)
    {
        int index = _potionSlots.IndexOf(potion);
        if (index < 0) throw new InvalidOperationException($"Tried to remove potion you don't have: {potion.Id}");

        _potionSlots[index] = null;

        UsedPotionRemoved?.Invoke(potion);
    }

    /// <summary>
    /// Populates the character's starting deck to start a new game.
    /// </summary>
    private void PopulateStartingDeck()
    {
        List<CardModel> deck = [];

        foreach (CardModel canonicalCard in Character.StartingDeck)
        {
            // We use ToMutable directly here instead of IClimbState.CreateCard, because this method is meant to be
            // called during player creation, before they've been added to a climb.
            // Once the player is added to a climb, this card will be added to it as well.
            CardModel mutableCard = canonicalCard.ToMutable();

            // We set this to 1 so that in the run history, it looks like it was received in Neow's room.
            mutableCard.FloorAddedToDeck = 1;

            deck.Add(mutableCard);
        }

        PopulateDeck(deck);
    }

    /// <summary>
    /// Populates a player's existing deck.
    /// This can be from a multiplayer sync or from a save file.
    /// </summary>
    /// <param name="cards">Cards to load.</param>
    /// <param name="silent">
    /// Whether or not to emit events. If loading from a multiplayer sync, we don't want to emit events.
    /// </param>
    private void PopulateDeck(IEnumerable<CardModel> cards, bool silent = false)
    {
        if (Deck.Cards.Any()) throw new InvalidOperationException("Deck has already been populated.");

        foreach (CardModel card in cards)
        {
            Deck.AddInternal(card, -1, silent); // Use instead of command so we don't trigger hooks.
        }
    }

    private void PopulateStartingRelics()
    {
        List<RelicModel> startingRelics = Character.StartingRelics.Select(r => r.ToMutable()).ToList();

        foreach (RelicModel relic in startingRelics)
        {
            // We set this to 1 so that, in the run history, it looks like it was received in Neow's room.
            relic.FloorAddedToDeck = 1;
            SaveManager.Instance.MarkRelicAsSeen(relic);
        }

        PopulateRelics(startingRelics);
    }

    /// <summary>
    /// Loads a player's existing relic set.
    /// This can be from the player's starting relics, from a multiplayer sync, or from a save file.
    /// </summary>
    /// <param name="relics">Relics to load.</param>
    /// <param name="silent">
    /// Whether or not to emit events. If loading from a multiplayer sync, we don't want to emit events.
    /// </param>
    private void PopulateRelics(IEnumerable<RelicModel> relics, bool silent = false)
    {
        if (Relics.Any()) throw new InvalidOperationException("Relics have already been populated.");

        foreach (RelicModel relic in relics)
        {
            AddRelicInternal(relic, -1, silent); // Use instead of command so we don't trigger hooks.
        }
    }

    /// <summary>
    /// Loads a player's existing potion set.
    /// This can be from a multiplayer sync or from a save file.
    /// </summary>
    /// <param name="serializablePotions">Cards to load.</param>
    /// <param name="silent">Whether or not to emit events. If loading from a multiplayer sync, we don't want to emit events.</param>
    private void LoadPotions(List<SerializablePotion> serializablePotions, bool silent = false)
    {
        if (Potions.Any()) throw new InvalidOperationException("Potions have already been populated.");

        foreach (SerializablePotion potion in serializablePotions)
        {
            AddPotionInternal(PotionModel.FromSerializable(potion), potion.SlotIndex, silent);
        }
    }

    /// <summary>
    /// Resets the player's combat state to an empty state.
    /// This will leave the player with no cards in combat, so you should usually call <see cref="PopulateCombatState"/>
    /// after.
    /// </summary>
    public void ResetCombatState()
    {
        PlayerCombatState = new PlayerCombatState(this);
    }

    /// <summary>
    /// Populates the player's combat state with everything they should get at the start of combat.
    /// For example, this clones all the cards from their deck into their draw pile in a random order.
    /// </summary>
    public void PopulateCombatState(Rng rng, CombatState state)
    {
        foreach (CardModel deckCard in Deck.Cards.ToList())
        {
            CardModel combatCard = state.CloneCard(deckCard);
            combatCard.DeckVersion = deckCard;
            PlayerCombatState!.DrawPile.AddInternal(combatCard);
        }

        PlayerCombatState!.DrawPile.RandomizeOrderInternal(this, rng, state);
    }

    /// <summary>
    /// Revives the player before the combat ends, in multiplayer only. Should only trigger when combat ends with other
    /// players alive.
    /// It is very important to do this _before_ combat ends instead of after. If the player is still dead during
    /// HookBus.AfterCombatEnd, their relics will not be subscribed to the HookBus, and relics which rely on AfterCombatEnd
    /// to reset state will not be reset for the next combat.
    /// See: Centennial Puzzle, Captain's Wheel, or any other relics that use AfterCombatEnd.
    /// </summary>
    public async Task ReviveBeforeCombatEnd()
    {
        if (Creature.IsDead)
        {
            await CreatureCmd.Heal(Creature, 1m);
        }
    }

    /// <summary>
    /// Called after combat ends, giving the player the opportunity to do things like clear out their combat state and
    /// other combat teardown stuff.
    /// </summary>
    public void AfterCombatEnd()
    {
        Creature.RemoveAllPowersExcept();
        PlayerCombatState?.AfterCombatEnd();

        // We don't want the block breaking animation, so clear block using the internal call
        Creature.LoseBlockInternal(Creature.Block);
    }

    private void OnRelicFlashed(RelicModel relic, IEnumerable<Creature> targets)
    {
        foreach (Creature target in targets)
        {
            NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(NRelicFlashVfx.Create(relic, target));
        }
    }

    public void OnSideSwitch()
    {
        // No-op for now.
    }

    /// <summary>
    /// Called from Creature when the player reaches zero health, after all hooks that prevent death are called.
    /// </summary>
    public void DieInternal()
    {
        IsActiveForHooks = false;
    }

    /// <summary>
    /// Called from Creature when the player changes from a dead state to a non-dead state.
    /// This is _not_ called in the scenario when death is prevented, e.g. by Fairy in a Bottle.
    /// Likely only called in multiplayer scenarios - players cannot revive in singleplayer (remember that death
    /// prevention is different).
    /// </summary>
    public void ReviveInternal()
    {
        IsActiveForHooks = true;
    }
}
