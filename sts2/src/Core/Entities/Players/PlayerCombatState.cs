using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Orbs;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.Entities.Players;

public class PlayerCombatState
{
    private readonly Player _player;
    private readonly List<Creature> _pets = [];

    public IReadOnlyList<Creature> Pets => _pets;

    // If more fields are added, remember to add them to NetFullCombatState
    private CardPile[]? _piles;
    private int _energy;
    private int _stars;

    public event Action<int, int>? EnergyChanged;
    public event Action<int, int>? StarsChanged;

    #region Card Piles

    public CardPile Hand { get; } = new(PileType.Hand);
    public CardPile DrawPile { get; } = new(PileType.Draw);
    public CardPile DiscardPile { get; } = new(PileType.Discard);
    public CardPile ExhaustPile { get; } = new(PileType.Exhaust);
    public CardPile PlayPile { get; } = new(PileType.Play);

    public IReadOnlyList<CardPile> AllPiles
    {
        get
        {
            _piles ??=
            [
                Hand,
                DrawPile,
                DiscardPile,
                ExhaustPile,
                PlayPile
            ];

            return _piles;
        }
    }

    public IEnumerable<CardModel> AllCards => AllPiles.SelectMany(p => p.Cards);

    #endregion

    public int Energy
    {
        get => _energy;
        set
        {
            if (_energy == value) return;

            int oldEnergy = _energy;
            _energy = value;
            EnergyChanged?.Invoke(oldEnergy, _energy);
        }
    }

    public int MaxEnergy => (int)Hook.ModifyMaxEnergy(_player.Creature.CombatState!, _player, _player.MaxEnergy);

    public int Stars
    {
        get => _stars;
        set
        {
            if (_stars == value) return;

            int oldStars = _stars;
            _stars = value;

            // This needs to occur before StarsChanged is invoked so that cards that base their vars on combat history
            // have the correct amount on their description
            CombatManager.Instance.History.StarsModified(_stars - oldStars, _player);

            StarsChanged?.Invoke(oldStars, _stars);
        }
    }

    public OrbQueue OrbQueue { get; }

    public PlayerCombatState(Player player)
    {
        _player = player;

        CombatManager.Instance.StateTracker.Subscribe(this);

        foreach (CardPile pile in AllPiles)
        {
            CombatManager.Instance.StateTracker.Subscribe(pile);
        }

        OrbQueue = new OrbQueue(player);
        OrbQueue.Clear();
        OrbQueue.AddCapacity(player.BaseOrbSlotCount);
    }

    public void AfterCombatEnd()
    {
        CombatManager.Instance.StateTracker.Unsubscribe(this);

        foreach (CardPile pile in AllPiles)
        {
            pile.Clear();
            CombatManager.Instance.StateTracker.Unsubscribe(pile);
        }

        _pets.Clear();
    }

    #region Energy

    public void ResetEnergy()
    {
        Energy = MaxEnergy;
    }

    public void AddMaxEnergyToCurrent()
    {
        Energy += MaxEnergy;
    }

    public void LoseEnergy(decimal amount)
    {
        if (amount < 0) throw new ArgumentException("Must not be negative.", nameof(amount));

        Energy = (int)Math.Max(Energy - amount, 0);
    }

    public void GainEnergy(decimal amount)
    {
        if (amount < 0) throw new ArgumentException("Must not be negative.", nameof(amount));

        Energy = (int)Math.Max(Energy + amount, 0);
    }

    public bool HasEnoughResourcesFor(CardModel card, out UnplayableReason reason)
    {
        int energyToSpend = Math.Max(0, card.EnergyCost.GetWithModifiers(CostModifiers.All));
        int starsToSpend = Math.Max(0, card.GetStarCostWithModifiers());

        if (energyToSpend > Energy && card.CombatState != null && Hook.ShouldPayExcessEnergyCostWithStars(card.CombatState, _player))
        {
            // For now, we assume that the only type of conversion we do is 1 energy -> 2 stars. If we ever have other
            // types of conversions, we can throw another hook in here.
            starsToSpend += (energyToSpend - Energy) * 2;
            energyToSpend = Energy;
        }

        reason = UnplayableReason.None;

        if (energyToSpend > Energy)
        {
            reason |= UnplayableReason.EnergyCostTooHigh;
        }

        if (starsToSpend > Stars)
        {
            reason |= UnplayableReason.StarCostTooHigh;
        }

        return reason == UnplayableReason.None;
    }

    #endregion

    #region Stars

    public void LoseStars(decimal amount)
    {
        if (amount < 0) throw new ArgumentException("Must not be negative.", nameof(amount));

        Stars = (int)Math.Max(Stars - amount, 0);
    }

    public void GainStars(decimal amount)
    {
        if (amount < 0) throw new ArgumentException("Must not be negative.", nameof(amount));
        Stars = (int)Math.Max(Stars + amount, 0);
    }

    #endregion

    #region Pets

    /// <summary>
    /// NEVER CALL THIS!
    /// ONLY <see cref="PlayerCmd.AddPet{T}"/> and save/load stuff should be calling this.
    /// </summary>
    /// <param name="pet">Pet to add.</param>
    public void AddPetInternal(Creature pet)
    {
        pet.Monster!.AssertMutable();

        // Don't double-add pets.
        // This can happen for pets like Osty that remain in combat after dying and then get revived.
        if (_pets.Contains(pet)) return;

        if (pet.PetOwner != _player)
        {
            // Check for existing ownership in case we're resurrecting a pet we already owned.
            pet.PetOwner = _player;
        }

        pet.Died += OnPetDied;
        _pets.Add(pet);
    }

    /// <summary>
    /// Get one of this player's pets.
    /// If the player has multiple of the same type of pet, just get the first one.
    /// If the player has none of this type of pet, returns null.
    /// </summary>
    /// <typeparam name="T">Type of pet to get.</typeparam>
    /// <returns>Matching pet.</returns>
    public Creature? GetPet<T>() where T : MonsterModel => Pets.FirstOrDefault(p => p.Monster is T);

    #endregion

    public void RecalculateCardValues()
    {
        foreach (CardModel card in AllCards)
        {
            card.RecalculateValues();
            card.Enchantment?.RecalculateValues();
        }
    }

    public void EndOfTurnCleanup()
    {
        foreach (CardModel card in AllCards)
        {
            card.EndOfTurnCleanup();
        }
    }

    public bool HasCardsToPlay()
    {
        return Hand.Cards.Any(c => c.CanPlay());
    }

    private void OnPetDied(Creature pet)
    {
        if (!_pets.Contains(pet)) throw new InvalidOperationException($"Player does not have pet {pet.Name}");

        // Don't remove the dead pet from the player's pet list if the pet isn't going to be removed from combat.
        if (!Hook.ShouldCreatureBeRemovedFromCombatAfterDeath(pet.CombatState!, pet)) return;

        pet.Died -= OnPetDied;
        _pets.Remove(pet);
    }
}
