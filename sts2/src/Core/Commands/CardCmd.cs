using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Commands;

public static class CardCmd
{
    /// <summary>
    /// Automatically play a card for free. Used for non-player-choice card playing effects.
    /// </summary>
    /// <example>
    /// Examples of where this would be used:
    /// * Havoc ("Play the top card of your Draw Pile and Exhaust it.")
    /// * Duplication Potion ("This turn, your next card is played twice.")
    /// </example>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="card">Card to autoplay.</param>
    /// <param name="target">Target for the autoplay. Will be randomized if null.</param>
    /// <param name="type">Type of autoplay. Certain checks may be bypassed for different autoplay types.</param>
    public static async Task AutoPlay(PlayerChoiceContext choiceContext, CardModel card, Creature? target, AutoPlayType type = AutoPlayType.Default)
    {
        if (CombatManager.Instance.IsAboutToEnd) return;

        // If the card has the Unplayable keyword, just discard it (or exhaust if we're forcing that).
        // An exception is made for Sly cards which we are currently discarding, as the Sly keyword bypasses Unplayable.
        if (card.Keywords.Contains(CardKeyword.Unplayable) && type != AutoPlayType.SlyDiscard)
        {
            await MoveToResultPileWithoutPlaying(choiceContext, card);
            return;
        }

        // Exit early if the card play is blocked by a hook listener (like the Normality curse).
        if (!Hook.ShouldPlay(card.CombatState!, card, out AbstractModel? preventer, type))
        {
            await MoveToResultPileWithoutPlaying(choiceContext, card);

            // Make the character say something so the player knows what happened
            LocString? locString = UnplayableReason.BlockedByHook.GetPlayerDialogueLine(preventer);

            if (locString != null)
            {
                NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(
                    NThoughtBubbleVfx.Create(locString.GetFormattedText(), card.Owner.Creature, 1f)
                );
            }

            return;
        }

        if (card.TargetType == TargetType.AnyEnemy)
        {
            target ??= card.Owner.ClimbState.Rng.CombatTargets.NextItem(card.CombatState!.HittableEnemies);

            // If the card is supposed to have a single target but no hittable enemies remain, skip playing it.
            // This almost never happens, because usually having no hittable enemies means combat is over.
            // However, it's not impossible. For example, if the combat contains a single enemy that revives itself
            // after dying (like Decimillipede or Test Subject) and that enemy is killed while multiple targeted
            // attacks are queued for autoplay (like playing Calculated Gamble with a hand full of Sly attacks),
            // there will be no hittable enemies for queued attacks to target, but combat will not end.
            if (target == null)
            {
                await MoveToResultPileWithoutPlaying(choiceContext, card);
                return;
            }
        }

        if (card.TargetType == TargetType.AnyAlly)
        {
            target ??= card.Owner.ClimbState.Rng.CombatTargets.NextItem(card.CombatState!.Allies.Where(c => c.IsAlive && c != card.Owner.Creature));

            // if the card has a single target, but you have no teammates remaining, skip playing it.
            if (target == null)
            {
                await MoveToResultPileWithoutPlaying(choiceContext, card);
                return;
            }
        }

        // We do this because X-cost cards should be auto-played at full energy/stars without using energy/stars.
        // See https://slay-the-spire.fandom.com/wiki/Mayhem for more.
        // However, we skip this for duped cards, because they already had the correct value set
        // when they were copied from the original.
        if (!card.IsDupe)
        {
            PlayerCombatState combatState = card.Owner.PlayerCombatState!;

            if (card.EnergyCost.CostsX)
            {
                card.EnergyCost.CapturedXValue = combatState.Energy;
            }

            if (card.HasStarCostX)
            {
                card.LastStarsSpent = combatState.Stars;
            }
            else
            {
                card.LastStarsSpent = Math.Max(0, card.GetStarCostWithModifiers());
            }
        }

        if (card.Pile == null)
        {
            await CardPileCmd.Add(card, PileType.Play);
        }

        _ = TaskHelper.RunSafely(card.OnEnqueuePlayVfx(target));

        await Hook.BeforeCardAutoPlayed(card.CombatState!, card, target, type);

        await card.OnPlayWrapper(choiceContext, target, true);
    }

    private static async Task MoveToResultPileWithoutPlaying(PlayerChoiceContext choiceContext, CardModel card)
    {
        await CardPileCmd.Add(card, PileType.Play);
        await card.MoveToResultPileWithoutPlaying(choiceContext);
    }

    /// <summary>
    /// Discard a card.
    /// WARNING: If you're discarding multiple cards at once for an effect like Concentrate or Gambler's Brew, do NOT
    /// use this method inside a loop, because the timing of the Sly effect will be incorrect. Instead, use the overload
    /// of this method that takes an IEnumerable.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="card">Card to discard.</param>
    public static async Task Discard(PlayerChoiceContext choiceContext, CardModel card)
    {
        await Discard(choiceContext, [card]);
    }

    /// <summary>
    /// Discard multiple cards.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="cards">Cards to discard.</param>
    public static async Task Discard(PlayerChoiceContext choiceContext, IEnumerable<CardModel> cards)
    {
        await DiscardAndDraw(choiceContext, cards, 0);
    }

    /// <summary>
    /// Discard cards, then draw cards.
    /// Unlike calling <see cref="Discard(PlayerChoiceContext,CardModel)"/> followed by
    /// <see cref="CardPileCmd.Draw(PlayerChoiceContext,Player)"/>, this will wait to trigger the discard-related hooks
    /// until after the draw is complete.
    /// Good for effects like <see cref="CalculatedGamble"/>.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="cardsToDiscard">Cards to discard.</param>
    /// <param name="cardsToDraw">Number of cards to draw.</param>
    public static async Task DiscardAndDraw(PlayerChoiceContext choiceContext, IEnumerable<CardModel> cardsToDiscard, int cardsToDraw)
    {
        if (CombatManager.Instance.IsAboutToEnd) return;
        if (!cardsToDiscard.Any()) return;

        List<CardModel> discardCards = cardsToDiscard.ToList();
        List<CardModel> slyCards = [];
        CardPile discardPile = PileType.Discard.GetPile(discardCards[0].Owner);

        // Discard all the cards first.
        foreach (CardModel card in discardCards)
        {
            if (card.Keywords.Contains(CardKeyword.Sly))
            {
                slyCards.Add(card);
            }

            await CardPileCmd.Add(card, discardPile);
            CombatManager.Instance.History.CardDiscarded(card);
            await Hook.AfterCardDiscarded(card.CombatState!, choiceContext, card);
        }

        // After all discards are finished, invoke the discard pile's ContentsChanged event one more time, just to make
        // sure the UI has a fresh view of the discard history.
        discardPile.InvokeContentsChanged();

        // Once all cards have been discarded, draw if necessary.
        if (cardsToDraw > 0)
        {
            await CardPileCmd.Draw(choiceContext, cardsToDraw, discardCards[0].Owner);
        }

        // Once all cards have been discarded, play the ones with Sly.
        // Doing it in this order prevents tricky interactions with Sly cards that discard other cards.
        foreach (CardModel slyCard in slyCards)
        {
            await AutoPlay(choiceContext, slyCard, null, AutoPlayType.SlyDiscard);
        }
    }

    /// <summary>
    /// Downgrades a card to its base form.
    /// Keeps things like enchantments and conditions.
    /// </summary>
    /// <param name="card">Card to exhaust.</param>
    public static void Downgrade(CardModel card)
    {
        if (CombatManager.Instance.IsAboutToEnd) return;

        if (card.Pile is { Type: PileType.Deck })
        {
            card.Owner.ClimbState.CurrentMapPointHistoryEntry?.GetEntry(card.Owner.NetId).DowngradedCards.Add(card.Id);
        }

        card.DowngradeInternal();
    }

    /// <summary>
    /// Exhaust a card.
    /// Note: do NOT make a bulk version of this; the hooks for one exhausted card should fully run before the next
    /// card starts exhausting.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="card">Card to exhaust.</param>
    /// <param name="causedByEthereal">
    /// Was this Exhaust caused by Ethereal?
    /// This should always be false except the specific case in <see cref="CombatManager"/>.
    /// </param>
    public static async Task Exhaust(PlayerChoiceContext choiceContext, CardModel card, bool causedByEthereal = false)
    {
        if (CombatManager.Instance.IsAboutToEnd) return;

        await CardPileCmd.Add(card, PileType.Exhaust);

        CombatManager.Instance.History.CardExhausted(card);
        await Hook.AfterCardExhausted(card.CombatState!, choiceContext, card, causedByEthereal);
    }

    /// <summary>
    /// Upgrade a card.
    /// Use this for actually upgrading a card, not for previewing an upgrade, because it won't highlight value changes.
    /// </summary>
    /// <param name="card">Card to upgrade.</param>
    /// <param name="style">How the upgraded card is displayed to the player.</param>
    public static void Upgrade(CardModel card, CardPreviewStyle style = CardPreviewStyle.HorizontalLayout)
    {
        Upgrade([card], style);
    }

    /// <summary>
    /// Upgrade a set of cards.
    /// Use this for actually upgrading cards, not for previewing upgrades, because it won't highlight value changes.
    /// </summary>
    /// <param name="cards">Cards to upgrade.</param>
    /// <param name="style">How multiple cards are aligned if previewed together.</param>
    public static void Upgrade(IEnumerable<CardModel> cards, CardPreviewStyle style)
    {
        if (CombatManager.Instance.IsAboutToEnd) return;

        foreach (CardModel card in cards)
        {
            if (!card.IsUpgradable) continue;

            if (card.Pile is { Type: PileType.Deck })
            {
                card.Owner.ClimbState.CurrentMapPointHistoryEntry?.GetEntry(card.Owner.NetId).UpgradedCards.Add(card.Id);
            }

            card.UpgradeInternal();
            card.FinalizeUpgradeInternal();

            if (LocalContext.IsMine(card) && card.Pile is { Type: PileType.Deck } && style != CardPreviewStyle.None)
            {
                Control? container = style switch
                {
                    CardPreviewStyle.EventLayout => NClimb.Instance?.GlobalUi.EventCardPreviewContainer,
                    CardPreviewStyle.HorizontalLayout => NClimb.Instance?.GlobalUi.CardPreviewContainer,
                    CardPreviewStyle.MessyLayout => NClimb.Instance?.GlobalUi.MessyCardPreviewContainer,
                    _ => throw new ArgumentOutOfRangeException(nameof(style), $"Unexpected {nameof(CardPreviewStyle)} {style}!")
                };

                container?.AddChildSafely(NCardUpgradeVfx.Create(card));
            }
        }
    }

    #region Transformation

    /// <summary>
    /// Transform a card to another randomly-selected card.
    /// </summary>
    /// <param name="original">Card to transform from.</param>
    /// <param name="rng">RNG to use for random card.</param>
    /// <param name="playAnim">Whether the transform animation should be played.</param>
    /// <returns>The replacement card.</returns>
    public static async Task<CardPileAddResult> TransformToRandom(CardModel original, Rng rng, bool playAnim)
    {
        IEnumerable<CardPileAddResult> result = await Transform(new CardTransformation(original).Yield(), rng, playAnim);
        return result.First();
    }

    /// <summary>
    /// Transform a card into a new card of the specified type.
    /// </summary>
    /// <param name="original">Card to transform from.</param>
    /// <param name="playAnim">Whether the transform animation should be played.</param>
    /// <typeparam name="T">Card type to transform to.</typeparam>
    /// <returns>The replacement card.</returns>
    public static async Task<CardPileAddResult?> TransformTo<T>(CardModel original, bool playAnim) where T : CardModel
    {
        CardModel replacement = original.CardScope!.CreateCard<T>(original.Owner);

        return await Transform(original, replacement, playAnim);
    }

    /// <summary>
    /// Transform a card to another card.
    /// </summary>
    /// <param name="original">Card to transform from.</param>
    /// <param name="replacement">Card to transform to.</param>
    /// <param name="playAnim">Whether the transform animation should be played.</param>
    /// <returns>The replacement card.</returns>
    public static async Task<CardPileAddResult?> Transform(CardModel original, CardModel replacement, bool playAnim)
    {
        // Pass null for RNG here. We are confident we won't need it, since replacement is directly specified
        IEnumerable<CardPileAddResult> result = await Transform(new CardTransformation(original, replacement).Yield(), null, playAnim);
        return result.FirstOrDefault();
    }

    /// <summary>
    /// Comparer to sort a list of CardPile & card index in ascending card index order.
    /// </summary>
    private static int PileIndexSort((CardTransformation, CardPile, int) value1, (CardTransformation, CardPile, int) value2)
    {
        if (value1.Item2.Type != value2.Item2.Type)
        {
            return value1.Item2.Type.CompareTo(value2.Item2.Type);
        }

        return value1.Item3.CompareTo(value2.Item3);
    }

    /// <summary>
    /// Transforms several cards to other cards.
    /// </summary>
    /// <param name="transformations">Cards to transform.</param>
    /// <param name="rng">Random number generator to use when choosing from random options.</param>
    /// <param name="playAnim">Whether the transform animation should be played.</param>
    /// <returns>The replacement card.</returns>
    public static async Task<IEnumerable<CardPileAddResult>> Transform(IEnumerable<CardTransformation> transformations, Rng? rng, bool playAnim)
    {
        if (CombatManager.Instance.IsAboutToEnd) return [];

        // Copy the transformation list. If the original pile is passed, we can run into an iteration exception when we
        // remove the card.
        CardTransformation[] transformationsArr = transformations.ToArray();

        List<(CardTransformation, CardPile, int)> transformationsWithOriginalData = [];

        // First remove all cards to be transformed. This allows the deck count to properly update before the animation begins
        foreach (CardTransformation transformation in transformationsArr)
        {
            transformation.Original.AssertMutable();

            if (!transformation.Original.IsTransformable)
            {
                throw new InvalidOperationException(
                    $"Can't transform {transformation.Original.Id.Entry} because it's un-transformable."
                );
            }

            CardPile? pile = transformation.Original.Pile;

            if (pile == null)
            {
                throw new InvalidOperationException(
                    $"Can't transform {transformation.Original.Id.Entry} because it has no pile."
                );
            }

            int cardIndex = pile.Cards.IndexOf(transformation.Original);

            transformation.Original.RemoveFromCurrentPile();
            transformationsWithOriginalData.Add((transformation, pile, cardIndex));
        }

        // Sort the cards to transform in ascending order so that if indexes have been removed, it's back
        // by the time we go to insert it
        transformationsWithOriginalData.Sort(PileIndexSort);

        List<CardPileAddResult> results = [];

        // Now add all replacement cards
        foreach ((CardTransformation transformation, CardPile pile, int cardIndex) in transformationsWithOriginalData)
        {
            CardModel original = transformation.Original;
            CardModel? replacement = transformation.GetReplacement(rng);
            IClimbState climbState = original.Owner.ClimbState;

            if (replacement == null) throw new InvalidOperationException($"Attempting to transform un-transformable card {original}!");

            replacement.AssertMutable();

            CardPileAddResult result = new()
            {
                success = true,
                cardAdded = replacement,
                modifyingModels = null
            };

            if (replacement.Owner != original.Owner)
            {
                throw new InvalidOperationException($"Attempting to transform card {original} to {replacement}, but the replacement has a different owner!");
            }

            // Only log transformations if they are permanent (in deck)
            if (pile.Type == PileType.Deck)
            {
                CardModel overriddenCard = Hook.ModifyCardBeingAddedToDeck(climbState, replacement, out List<AbstractModel> modifyingModels);
                replacement = overriddenCard;
                result.cardAdded = replacement;
                result.modifyingModels = modifyingModels;

                replacement.FloorAddedToDeck = climbState.TotalFloor;
                climbState.CurrentMapPointHistoryEntry?.GetEntry(original.Owner.NetId)
                    .CardsTransformed
                    .Add(new CardTransformationHistoryEntry(original.Id, replacement.Id));
            }

            // Instead of CardPileCmd.Add, this uses internal methods because, from the player's perspective,
            // the "heart of the card" is being changed, rather than one card being removed and another being added.

            switch (pile.Type)
            {
                // Deck cards are added at the bottom
                case PileType.Deck:
                    pile.AddInternal(replacement);
                    break;
                default:
                    // All combat cards are added back where they were
                    pile.AddInternal(replacement, cardIndex);

                    // Assumes that monsters never trigger card transformations.
                    CombatManager.Instance.History.CardGenerated(replacement, true);

                    await Hook.AfterCardEnteredCombat(replacement.CombatState!, replacement);
                    break;
            }

            await Hook.AfterCardChangedPiles(climbState, original.CombatState, replacement, pile.Type, null);
            pile.InvokeCardAddFinished();

            original.AfterTransformedFrom();
            replacement.AfterTransformedTo();
            results.Add(result);
        }

        List<Task> tasksToAwait = [];

        // Play VFX for the transforming cards. Only VFX should go in this section!
        for (int i = 0; i < results.Count; i++)
        {
            CardModel original = transformationsWithOriginalData[i].Item1.Original;
            CardModel replacement = results[i].cardAdded;

            // Don't play VFX for non-owning players in multiplayer
            if (!LocalContext.IsMine(replacement)) continue;

            // Cards in hand are special and need to be animated in-place
            if (replacement.Pile!.Type == PileType.Hand)
            {
                if (TestMode.IsOn) continue;

                NCardPlayQueue playQueue = NCombatRoom.Instance!.Ui.PlayQueue;
                NPlayerHand hand = NCombatRoom.Instance.Ui.Hand;

                // If the player tried to play the card before it finished the transform VFX, the card might be in the
                // play queue instead of the hand, so use FindOnTable instead of going straight to the hand node
                NCard? cardNode = NCard.FindOnTable(original, PileType.Hand);

                if (cardNode == null) throw new InvalidOperationException($"Couldn't get hand node for original card {transformationsArr[i].Original}!");

                if (playQueue.IsAncestorOf(cardNode))
                {
                    // The card node is in the play queue, and it's going to get cancelled once the transform occurs
                    // anyway, so cancel it now to return it to the hand so we can play our animation.
                    playQueue.RemoveCardFromQueueForCancellation(cardNode);

                    // Since the original card is no longer in the hand pile, the play queue does not know to put the
                    // card back into the hand. We do it ourselves, because we know better.
                    NPlayerHand.Instance!.Add(cardNode);
                }

                // If the card is currently being dragged by the player, put it back into the hand node.
                hand.TryCancelCardPlay(original);

                // We need all cards in hand to have final updated info before anything else happens, but we also
                // want to animate the cards quickly rather than waiting one-by-one, so defer waiting on the transformations
                tasksToAwait.Add(TaskHelper.RunSafely(NCardTransformVfx.PlayAnimOnCardInHand(cardNode, replacement)));
                await Cmd.Wait(0.2f);
            }
            else if (playAnim)
            {
                NClimb.Instance?.GlobalUi.CardPreviewContainer.AddChildSafely(
                    NCardTransformVfx.Create(original, replacement, results[i].modifyingModels?.OfType<RelicModel>())
                );
            }
        }

        await Task.WhenAll(tasksToAwait);

        for (int i = 0; i < results.Count; i++)
        {
            CardPileAddResult result = results[i];

            if (result.success && result.cardAdded.Pile!.Type.IsCombatPile())
            {
                // There are currently no monsters that transform player cards, so it's safe to assume the addedByPlayer
                // argument is true. If we run into any bugs here in the future, we should add a transformedByPlayer
                // argument to all Transform methods.
                await Hook.AfterCardGeneratedForCombat(result.cardAdded.CombatState!, result.cardAdded, true);
            }

            // Only remove the original card completely after all logic is complete.
            transformationsWithOriginalData[i].Item1.Original.RemoveFromState();
        }

        return results;
    }

    #endregion

    /// <summary>
    /// Apply an Enchantment to a card.
    /// </summary>
    /// <param name="card">Card to enchant.</param>
    /// <param name="amount">Amount of the enchantment to apply.</param>
    /// <typeparam name="T">Type of enchantment to apply.</typeparam>
    /// <returns>Enchantment that was applied, or null if it failed.</returns>
    public static T? Enchant<T>(CardModel card, decimal amount) where T : EnchantmentModel
    {
        return Enchant(ModelDb.Enchantment<T>().ToMutable(), card, amount) as T;
    }

    /// <summary>
    /// Apply an Enchantment to a card.
    /// Use this for actually enchanting a card, not for previewing an enchantment, because it won't highlight value
    /// changes.
    /// </summary>
    /// <param name="enchantment">Enchantment to apply.</param>
    /// <param name="card">Card to enchant.</param>
    /// <param name="amount">Amount of the enchantment to apply.</param>
    /// <returns>Enchantment that was applied, or null if it failed.</returns>
    public static EnchantmentModel? Enchant(EnchantmentModel enchantment, CardModel card, decimal amount)
    {
        enchantment.AssertMutable();

        if (!enchantment.CanEnchant(card))
        {
            throw new InvalidOperationException($"Cannot enchant {card.Id} with {enchantment.Id}.");
        }

        if (card.Enchantment == null)
        {
            card.EnchantInternal(enchantment, amount);
            enchantment.ModifyCard();
        }
        else if (card.Enchantment.GetType() == enchantment.GetType())
        {
            card.Enchantment!.Amount += (int)amount;
        }
        else
        {
            throw new InvalidOperationException($"Cannot enchant {card.Id} with {enchantment.Id} because it already has enchantment {card.Enchantment.Id}.");
        }

        card.FinalizeUpgradeInternal();

        // If the card was already in your deck before being enchanted, record it in the climb history.
        // If the card was pre-enchanted before being added to your deck, don't record it.
        if (card.Pile != null)
        {
            card.Owner.ClimbState.CurrentMapPointHistoryEntry?.GetEntry(card.Owner.NetId).CardsEnchanted.Add(
                new CardEnchantmentHistoryEntry(card.Id, enchantment.Id)
            );
        }

        return card.Enchantment;
    }

    /// <summary>
    /// Clear a card's Enchantment if it has one.
    /// </summary>
    /// <param name="card">Card whose enchantment we want to clear.</param>
    public static void ClearEnchantment(CardModel card)
    {
        card.ClearEnchantmentInternal();
    }

    /// <summary>
    /// Apply Afflictions to cards and show it to the owning player if they are the local player.
    /// </summary>
    /// <param name="cards">Cards to afflict.</param>
    /// <param name="amount">Amount of the affliction to apply.</param>
    /// <param name="style">The style of preview to use.</param>
    /// <typeparam name="T">Type of affliction to apply.</typeparam>
    /// <returns>Afflictions that were applied.</returns>
    public static async Task<IEnumerable<T>> AfflictAndPreview<T>(IEnumerable<CardModel> cards, decimal amount, CardPreviewStyle style = CardPreviewStyle.HorizontalLayout) where T : AfflictionModel
    {
        List<T> afflictions = [];
        List<CardModel> cardList = [];

        foreach (CardModel card in cards)
        {
            T? affliction = await Afflict<T>(card, amount);

            if (affliction != null)
            {
                afflictions.Add(affliction);
                cardList.Add(card);
            }
        }

        if (cardList.Count > 0 && style != CardPreviewStyle.None)
        {
            if (cardList.Any(c => c.Owner != cardList[0].Owner))
            {
                throw new InvalidOperationException("All cards passed to AfflictAndPreview must have the same owner!");
            }

            if (LocalContext.IsMine(cardList[0]))
            {
                Preview(cardList, 1.2f, style);
                await Cmd.Wait(1.25f);
            }
        }

        return afflictions;
    }

    /// <summary>
    /// Apply an Affliction to a card.
    /// </summary>
    /// <param name="card">Card to afflict.</param>
    /// <param name="amount">Amount of the affliction to apply.</param>
    /// <typeparam name="T">Type of affliction to apply.</typeparam>
    /// <returns>Affliction that was applied, or null if it failed.</returns>
    public static async Task<T?> Afflict<T>(CardModel card, decimal amount) where T : AfflictionModel
    {
        return await Afflict(ModelDb.Affliction<T>().ToMutable(), card, amount) as T;
    }

    /// <summary>
    /// Apply an Affliction to a card.
    /// </summary>
    /// <param name="affliction">Affliction to apply.</param>
    /// <param name="card">Card to afflict.</param>
    /// <param name="amount">Amount of the affliction to apply.</param>
    /// <returns>Affliction that was applied, or null if it failed.</returns>
    public static Task<AfflictionModel?> Afflict(AfflictionModel affliction, CardModel card, decimal amount)
    {
        if (CombatManager.Instance.IsAboutToEnd) return Task.FromResult<AfflictionModel?>(null);

        affliction.AssertMutable();

        if (!Hook.ShouldAfflict(card.CombatState!, card, affliction)) return Task.FromResult<AfflictionModel?>(null);
        if (!affliction.CanAfflict(card)) return Task.FromResult<AfflictionModel?>(null);

        if (card.Affliction == null)
        {
            card.AfflictInternal(affliction, amount);
            affliction.AfterApplied();
        }
        else if (card.Affliction.GetType() == affliction.GetType())
        {
            card.Affliction!.Amount += (int)amount;
        }
        else
        {
            throw new InvalidOperationException($"Cannot afflict {card.Id} with {affliction.Id} because it already has affliction {card.Affliction.Id}.");
        }

        return Task.FromResult(card.Affliction);
    }

    /// <summary>
    /// Clear a card's Affliction if it has one.
    /// </summary>
    /// <param name="card">Card whose affliction we want to clear.</param>
    public static void ClearAffliction(CardModel card)
    {
        card.ClearAfflictionInternal();
    }

    /// <summary>
    /// Apply keywords to a card.
    /// </summary>
    /// <param name="card">Card to apply keyword too.</param>
    /// <param name="keywords">keywords to apply.</param>
    public static void ApplyKeyword(CardModel card, params CardKeyword[] keywords)
    {
        foreach (CardKeyword cardKeyword in keywords)
        {
            card.AddKeyword(cardKeyword);
        }

        NCard.FindOnTable(card)?.UpdateVisuals(card.Pile!.Type, CardPreviewMode.Normal);
    }

    public static void RemoveKeyword(CardModel card, params CardKeyword[] keywords)
    {
        foreach (CardKeyword cardKeyword in keywords)
        {
            card.RemoveKeyword(cardKeyword);
        }

        NCard.FindOnTable(card)?.UpdateVisuals(card.Pile!.Type, CardPreviewMode.Normal);
    }


    /// <summary>
    /// Creates a set of NCards that spawn in the middle of the screen, then fly to the pile they're in.
    /// Useful for when you want to preview cards that are being added to a pile.
    /// </summary>
    /// <param name="card">Card to preview.</param>
    /// <param name="time">How long the card lingers before it goes off screen</param>
    /// <param name="style">How multiple cards are aligned if previewed together.</param>
    public static TaskCompletionSource? Preview(CardModel card, float time = 1.2f, CardPreviewStyle style = CardPreviewStyle.HorizontalLayout)
    {
        return PreviewInternal(card, false, null, time, style);
    }

    /// <summary>
    /// Creates a set of NCards that spawn in the middle of the screen, then fly to the pile they're in.
    /// Useful for when you want to preview cards that are being added to a pile.
    /// </summary>
    /// <param name="cards">Cards to preview.</param>
    /// <param name="time">How long the card lingers before it goes off screen</param>
    /// <param name="style">How multiple cards are aligned if previewed together.</param>
    public static void Preview(IReadOnlyList<CardModel> cards, float time = 1.2f, CardPreviewStyle style = CardPreviewStyle.HorizontalLayout)
    {
        if (TestMode.IsOn) return;
        if (CombatManager.Instance.IsAboutToEnd) return;

        foreach (CardModel card in cards)
        {
            PreviewInternal(card, false, null, time, style);
        }
    }

    public static void PreviewCardPileAdd(CardPileAddResult result, float time = 1.2f, CardPreviewStyle style = CardPreviewStyle.HorizontalLayout)
    {
        if (TestMode.IsOn) return;
        if (CombatManager.Instance.IsAboutToEnd) return;
        if (!result.success) return;
        if (!LocalContext.IsMine(result.cardAdded)) return;

        PreviewInternal(result.cardAdded, true, result.modifyingModels?.OfType<RelicModel>() ?? null, time, style);
    }

    public static void PreviewCardPileAdd(IReadOnlyList<CardPileAddResult> results, float time = 1.2f, CardPreviewStyle style = CardPreviewStyle.HorizontalLayout)
    {
        if (TestMode.IsOn) return;
        if (CombatManager.Instance.IsAboutToEnd) return;

        if (results.Count > 5 && style == CardPreviewStyle.HorizontalLayout)
        {
            Log.Warn("Horizontal layout is being used with more than five cards! They will go offscreen");
        }

        foreach (CardPileAddResult result in results)
        {
            if (!result.success) continue;
            if (!LocalContext.IsMine(result.cardAdded)) continue;
            PreviewInternal(result.cardAdded, true, result.modifyingModels?.OfType<RelicModel>() ?? null, time, style);
        }
    }

    private static TaskCompletionSource? PreviewInternal(CardModel card, bool isAddingCardsToPile, IEnumerable<RelicModel>? relicsToFlash = null, float time = 1.2f, CardPreviewStyle style = CardPreviewStyle.HorizontalLayout)
    {
        // if the card has no pile (i.e. it was blocked from being added to the pile), skip it
        if (card.Pile == null) return null;
        if (TestMode.IsOn) return null;
        if (CombatManager.Instance.IsAboutToEnd) return null;
        if (!LocalContext.IsMine(card)) return null;

        // Get pile type before tween, in case combat ends in the middle of the tween.
        PileType pileType = card.Pile!.Type;
        NCard node = NCard.Create(card)!;

        Control? container = style switch
        {
            CardPreviewStyle.HorizontalLayout => NClimb.Instance?.GlobalUi.CardPreviewContainer,
            CardPreviewStyle.MessyLayout => NClimb.Instance?.GlobalUi.MessyCardPreviewContainer,
            _ => throw new ArgumentOutOfRangeException(nameof(style), $"Unexpected {nameof(CardPreviewStyle)} {style}!")
        };

        container?.AddChildSafely(node);
        node.UpdateVisuals(pileType, CardPreviewMode.Normal);

        TaskCompletionSource source = new();
        Tween tween = node.CreateTween();

        // First scale tween
        tween.TweenProperty(node, "scale", Vector2.One, 0.25)
            .From(Vector2.Zero)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);

        tween.TweenCallback(Callable.From(() => { TaskHelper.RunSafely(FlashRelics(node, relicsToFlash)); }));

        tween.TweenCallback(Callable.From(() =>
        {
            Node? vfxContainer;
            NCardFlyVfx? vfx = null;

            if (pileType == PileType.Deck)
            {
                // This renders the card behind the "View Deck" button but above the TopBar asset when adding cards to deck.
                vfxContainer = NClimb.Instance?.GlobalUi.TopBar.TrailContainer;
            }
            else
            {
                vfxContainer = NCombatRoom.Instance?.CombatVfxContainer;
            }

            if (vfxContainer != null)
            {
                // Check if the pile has changed.
                // If the pile type is somehow null (combat ends mid tween) then use the old pile
                PileType updatedPile = card.Pile != null ? card.Pile!.Type : pileType;

                Vector2 pilePos = updatedPile.GetTargetPosition(node);
                vfx = NCardFlyVfx.Create(node, pilePos, isAddingCardsToPile, card.Owner.Character.TrailPath);
            }

            if (vfx != null && vfxContainer != null)
            {
                vfxContainer.AddChildSafely(vfx);
                vfx.SwooshAwayCompletion!.Task.ContinueWith(_ => source.SetResult());
            }
            else
            {
                node.QueueFreeSafely();
                source.SetResult();
            }
        })).SetDelay(time);

        return source;
    }

    private static Task FlashRelics(NCard node, IEnumerable<RelicModel>? relicsToFlash)
    {
        if (relicsToFlash == null) return Task.CompletedTask;

        foreach (RelicModel relicModel in relicsToFlash)
        {
            relicModel.Flash();

            NRelicFlashVfx flashVfx = NRelicFlashVfx.Create(relicModel)!;
            node.AddChildSafely(flashVfx);
            flashVfx.Scale = Vector2.One * 2f;
            flashVfx.Position = node.Size * 0.5f;
        }

        return Task.CompletedTask;
    }
}
