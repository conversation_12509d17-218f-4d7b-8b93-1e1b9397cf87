using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Nodes.Screens;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Commands;

public static class RelicSelectCmd
{
    private static bool ShouldSelectLocalRelic(Player player)
    {
        return LocalContext.IsMe(player) && ClimbManager.Instance.NetService.Type != NetGameType.Replay;
    }

    public static async Task<RelicModel?> FromChooseARelicScreen(Player player, IReadOnlyList<RelicModel> relics)
    {
        RelicModel? result;

        uint choiceId = ClimbManager.Instance.PlayerChoiceSynchronizer.ReserveChoiceId(player);

        if (ShouldSelectLocalRelic(player))
        {
            // Show the "Choose a Relic" screen if we're in game mode.
            NChooseARelicSelection screen = NChooseARelicSelection.ShowScreen(relics)!;
            if (LocalContext.IsMe(player))
            {
                foreach (RelicModel relic in relics)
                {
                    SaveManager.Instance.MarkRelicAsSeen(relic);
                }
            }

            result = (await screen.RelicsSelected()).FirstOrDefault();
            int index = relics.IndexOf(result);
            PlayerChoiceResult choiceResult = PlayerChoiceResult.FromIndex(index);
            ClimbManager.Instance.PlayerChoiceSynchronizer.SyncLocalChoice(player, choiceId, choiceResult);
        }
        else
        {
            PlayerChoiceResult choiceResult = await ClimbManager.Instance.PlayerChoiceSynchronizer.WaitForRemoteChoice(player, choiceId);
            int index = choiceResult.AsIndex();
            result = index < 0 ? null : relics[index];
        }

        return result;
    }
}
