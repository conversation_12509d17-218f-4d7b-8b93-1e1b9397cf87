using MegaCrit.Sts2.Core.Timeline.Epochs;

namespace MegaCrit.Sts2.Core.Timeline.Stories;

// ReSharper disable once UnusedType.Global
// Reason: Instantiated via reflection.
public sealed class SilentStory : StoryModel
{
    public override string Id => "SILENT";

    public override EpochModel[] Epochs =>
    [
        // EpochModel.Get<Silent9Epoch>(),
        EpochModel.Get<Silent3Epoch>(),
        EpochModel.Get<Silent2Epoch>(),
        EpochModel.Get<Silent5Epoch>(),
        EpochModel.Get<Silent6Epoch>(),
        EpochModel.Get<Silent1Epoch>(),
        EpochModel.Get<Silent4Epoch>(),
        EpochModel.Get<Silent7Epoch>()
    ];
}
