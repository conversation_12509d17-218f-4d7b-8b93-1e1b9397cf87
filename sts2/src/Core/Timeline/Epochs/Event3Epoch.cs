using System.Collections.Generic;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Events;
using MegaCrit.Sts2.Core.Nodes.Screens.Timeline;

namespace MegaCrit.Sts2.Core.Timeline.Epochs;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class Event3Epoch : EpochModel
{
    public override EpochEra Era => EpochEra.BlightInfection;
    public override int EraPosition => 3;

    public static List<EventModel> Events =>
    [
        ModelDb.Event<ColorfulPhilosophers>()
    ];

    public override string UnlockText
    {
        get
        {
            LocString unlockText = new("epochs", $"{Id}.unlockText");
            unlockText.Add("Event", Events[0].Title);
            return unlockText.GetFormattedText();
        }
    }

    public override void QueueUnlocks()
    {
        NTimelineScreen.Instance.QueueMiscUnlock(UnlockText);
    }
}
