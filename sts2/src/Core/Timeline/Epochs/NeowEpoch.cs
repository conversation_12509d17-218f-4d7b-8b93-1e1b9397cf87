using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.Screens.Timeline;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Timeline.Epochs;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class NeowEpoch : EpochModel
{
    public override EpochEra Era => EpochEra.BlightContagion;
    public override int EraPosition => 0;

    public override void QueueUnlocks()
    {
        // The "You unlocked Neow" screen and text.
        LocString unlockText = new("epochs", $"{Id}.unlock");
        NTimelineScreen.Instance.QueueMiscUnlock($"[center]{unlockText.GetFormattedText()}[/center]");

        SaveManager.Instance.ObtainEpochOverride(GetId<Silent1Epoch>(), EpochState.ObtainedNoSlot);

        // Create a list of slots to unlock
        EpochModel[] unlockedSlots =
        [
            Get(GetId<Colorless1Epoch>()),
            Get(GetId<CompendiumEpoch>()),
            Get(GetId<CustomAndSeedsEpoch>()),
            Get(GetId<DailyClimbEpoch>()),
            Get(GetId<DarvEpoch>()),
            Get(GetId<Ironclad2Epoch>()),
            Get(GetId<Ironclad3Epoch>()),
            Get(GetId<Ironclad4Epoch>()),
            Get(GetId<Ironclad5Epoch>()),
            Get(GetId<Ironclad6Epoch>()),
            Get(GetId<Ironclad7Epoch>()),
            Get(GetId<OrobasEpoch>()),
            Get(GetId<Silent1Epoch>()),
            Get(GetId<UnderdocksEpoch>()),
            Get(GetId<ZlatirEpoch>())
        ];

        QueueTimelineExpansion(unlockedSlots);
    }
}
