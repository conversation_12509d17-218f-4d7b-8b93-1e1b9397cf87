using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.Screens.Timeline;

namespace MegaCrit.Sts2.Core.Timeline.Epochs;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class Silent1Epoch : EpochModel
{
    public override EpochEra Era => EpochEra.InvitationDoors;
    public override int EraPosition => 2;
    public override string StoryId => "Silent";

    public override void QueueUnlocks()
    {
        LocString unlockText = new("epochs", $"{Id}.unlock");
        NTimelineScreen.Instance.QueueMiscUnlock($"[center]{unlockText.GetFormattedText()}[/center]");

        // Create a list of slots to unlock
        EpochModel[] unlockedSlots =
        [
            Get(GetId<Regent1Epoch>()),
            Get(GetId<Silent2Epoch>()),
            Get(GetId<Silent3Epoch>()),
            Get(GetId<Silent4Epoch>()),
            Get(GetId<Silent5Epoch>()),
            Get(GetId<Silent6Epoch>()),
            Get(GetId<Silent7Epoch>())
        ];

        QueueTimelineExpansion(unlockedSlots);
    }
}
