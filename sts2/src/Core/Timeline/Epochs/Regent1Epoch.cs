using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.Screens.Timeline;

namespace MegaCrit.Sts2.Core.Timeline.Epochs;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class Regent1Epoch : EpochModel
{
    public override EpochEra Era => EpochEra.PrehistoriaTrees;
    public override int EraPosition => 0;
    public override string StoryId => "Regent";

    public override void QueueUnlocks()
    {
        LocString unlockText = new("epochs", $"{Id}.unlock");
        NTimelineScreen.Instance.QueueMiscUnlock($"[center]{unlockText.GetFormattedText()}[/center]");

        // Create a list of slots to unlock
        EpochModel[] unlockedSlots =
        [
            Get(GetId<Regent2Epoch>()),
            Get(GetId<Regent3Epoch>()),
            Get(GetId<Regent4Epoch>()),
            Get(GetId<Regent5Epoch>()),
            Get(GetId<Regent6Epoch>()),
            Get(GetId<Regent7Epoch>()),
            Get(GetId<Defect1Epoch>())
        ];

        QueueTimelineExpansion(unlockedSlots);
    }
}
