using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Climbs.Metrics;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Managers;
using MegaCrit.Sts2.Core.Saves.Migrations;
using MegaCrit.Sts2.Core.Saves.Test;
using MegaCrit.Sts2.Core.TestSupport;
using MegaCrit.Sts2.Core.Timeline;
using MegaCrit.Sts2.Core.Timeline.Epochs;
using MegaCrit.Sts2.Core.Unlocks;

namespace MegaCrit.Sts2.Core.Saves;

/// <summary>
/// Manages saving and loading game data, including settings, progress, current climbs, and climb history.
/// Implements the Singleton pattern for global access to save functionality.
/// </summary>
/// <remarks>
/// The SaveManager coordinates multiple specialized save managers:
/// - SettingsSaveManager: Handles user settings (not cloud synced)
/// - ProgressSaveManager: Manages global game progress and statistics
/// - ClimbSaveManager: Handles saving/loading of active climbs
/// - ClimbHistorySaveManager: Manages historical climb data
///
/// This specialized manager architecture improves testability by allowing each save manager
/// to be tested independently with appropriate mocks and stubs.
///
/// Save files are stored in a user-scoped, platform-specific location:
/// - In editor: C:\Users\<USER>\AppData\Roaming\SlayTheSpire2\{platform}\{userId}\saves
/// - In builds: C:\Users\<USER>\AppData\Roaming\Godot\app_userdata\sts2\{platform}\{userId}\saves
/// </remarks>
public class SaveManager
{
    public static SaveManager Instance
    {
        get
        {
            if (_instance == null)
            {
                ISaveStore fileIo;
                if (TestMode.IsOn)
                {
                    fileIo = new MockGodotFileIo("user://test");
                }
                else
                {
                    fileIo = new GodotFileIo(UserDataPathProvider.GetAccountScopedBasePath(null));
                }

                _instance = new SaveManager(fileIo);
            }

            return _instance;
        }
    }

    private static SaveManager? _instance;

    private readonly SettingsSaveManager _settingsSaveManager;
    private readonly ProgressSaveManager _progressSaveManager;
    private readonly ClimbSaveManager _climbSaveManager;
    private readonly ClimbHistorySaveManager _climbHistorySaveManager;
    private readonly PrefsSaveManager _prefsSaveManager;
    private readonly ProfileSaveManager _profileSaveManager;

    private readonly ISaveStore _saveStore;

    public SettingsSave SettingsSave => _settingsSaveManager.Settings; // NOTE: This save shouldn't be cloud synced!

    public PrefsSave PrefsSave => _prefsSaveManager.Prefs;

    public ProgressSave ProgressSave
    {
        get => _progressSaveManager.Progress;
        set => _progressSaveManager.Progress = value;
    }

    /// <summary>
    /// This is the set of unlocks that the progress file says that the local player should have.
    /// You should only be querying this from the main menu. When you are inside of a climb, you should be using
    /// ClimbState.UnlockState or Player.UnlockState.
    /// </summary>
    public UnlockState UnlockState => _progressSaveManager.UnlockState;

    public bool HasClimbSave => _climbSaveManager.HasClimbSave;
    public bool HasMultiplayerClimbSave => _climbSaveManager.HasMultiplayerClimbSave;
    public int CurrentProfileId { get; private set; }

    public event Action? Saved
    {
        add => _climbSaveManager.Saved += value;
        remove => _climbSaveManager.Saved -= value;
    }

    public event Action<int>? ProfileIdChanged;

    public static void MockInstanceForTesting(SaveManager saveManager)
    {
        _instance = saveManager;
    }

    public static void ClearInstanceForTesting()
    {
        _instance = null;
    }

    /// <summary>
    /// Constructor with dependency injection support.
    /// </summary>
    /// <param name="saveStore">The file I/O backend to use. If null, one will be created for the current save profile.</param>
    /// <param name="profileId">The profile ID to use. If null, it will be read from file.</param>
    /// <param name="forceSynchronous">Force all operations to be performed synchronously. Only use in tests.</param>
    public SaveManager(ISaveStore saveStore, int? profileId = null, bool forceSynchronous = false)
    {
        // Migrate legacy pre-account-scoped data to user-scoped directories before initializing managers
        AccountScopeUserDataMigrator.MigrateToUserScopedDirectories();
        AccountScopeUserDataMigrator.ArchiveLegacyData();

        // Then, migrate legacy pre-profile-scoped data to profile-scoped directories
        ProfileAccountScopeMigrator.MigrateToProfileScopedDirectories();
        ProfileAccountScopeMigrator.ArchiveLegacyData();

        _saveStore = saveStore;
        MigrationManager migrationManager = new(saveStore);

        _profileSaveManager = new ProfileSaveManager(saveStore, migrationManager);

        if (profileId == null)
        {
            // Load the profile save to figure out what profile ID to load
            _profileSaveManager.LoadProfile(); // We don't really care about the result of loading this, refactor later if necessary
            CurrentProfileId = _profileSaveManager.Profile.LastProfileId;
        }
        else
        {
            CurrentProfileId = profileId.Value;
        }

        // Log the full user-scoped path on startup
        string userScopedPath = ProjectSettings.GlobalizePath(UserDataPathProvider.GetProfileScopedBasePath(CurrentProfileId));
        Log.Info($"Profile-scoped data path initialized: {userScopedPath}");

        _settingsSaveManager = new SettingsSaveManager(saveStore, migrationManager);

        _progressSaveManager = new ProgressSaveManager(CurrentProfileId, saveStore, migrationManager);
        _climbSaveManager = new ClimbSaveManager(CurrentProfileId, saveStore, migrationManager, forceSynchronous);
        _climbHistorySaveManager = new ClimbHistorySaveManager(CurrentProfileId, saveStore, migrationManager);
        _prefsSaveManager = new PrefsSaveManager(CurrentProfileId, saveStore, migrationManager);
    }

    public string GetProfileScopedPath(string userData)
    {
        return _saveStore.GetFullPath(Path.Combine(UserDataPathProvider.GetProfileDir(CurrentProfileId), userData));
    }

    public void SwitchProfileId(int profileId)
    {
        Log.Info($"Switching save profiles to {profileId}");

        CurrentProfileId = profileId;

        _progressSaveManager.CurrentProfileId = profileId;
        _climbSaveManager.CurrentProfileId = profileId;
        _climbHistorySaveManager.CurrentProfileId = profileId;
        _prefsSaveManager.CurrentProfileId = profileId;

        _profileSaveManager.Profile.LastProfileId = profileId;
        _profileSaveManager.SaveProfile();

        ProfileIdChanged?.Invoke(profileId);
    }

    /// <summary>
    /// Resets the static instance.
    /// For testing purposes only!
    /// </summary>
    public static void ResetInstance()
    {
        _instance = null;
    }

    /// <summary>
    /// Sets a custom instance.
    /// For testing purposes only!
    /// </summary>
    public static void SetInstance(SaveManager customInstance)
    {
        _instance = customInstance;
    }

    public Task SaveClimb(AbstractRoom? preFinishedRoom)
    {
        SaveProgressFile();
        return _climbSaveManager.SaveClimb(preFinishedRoom);
    }

    /// <summary>
    /// Called whenever the player wins, loses, or abandons climb.
    /// Updates the progress.save file using the current_climb.save file or similar.
    /// </summary>
    /// <param name="serializableClimb">The serialized climb data.</param>
    /// <param name="victory">Whether or not the climb ended in a victory.</param>
    public void UpdateProgressSaveWithClimbData(SerializableClimb serializableClimb, bool victory)
    {
        _progressSaveManager.UpdateWithClimbData(serializableClimb, victory);
    }

    /// <summary>
    /// Called when the player wins a combat.
    /// </summary>
    public void UpdateProgressSaveAfterCombatWon(Player localPlayer, CombatRoom combatRoom)
    {
        _progressSaveManager.UpdateAfterCombatWon(localPlayer, combatRoom);
    }

    public CharacterStats GetCharacterStats(ModelId characterId)
    {
        return _progressSaveManager.GetCharacterStats(characterId);
    }

    private CardStats GetCardStats(ModelId cardId)
    {
        return _progressSaveManager.GetCardStats(cardId);
    }

    public EncounterStats? GetEncounterStats(ModelId characterId)
    {
        return _progressSaveManager.GetEncounterStats(characterId);
    }

    public void DeleteCurrentClimb()
    {
        _climbSaveManager.DeleteCurrentClimb();
    }

    public void DeleteCurrentMultiplayerClimb()
    {
        _climbSaveManager.DeleteCurrentMultiplayerClimb();
    }

    public void DeleteProfile(int profileId)
    {
        string profilePath = UserDataPathProvider.GetProfileScopedBasePath(profileId);
        Log.Info($"Deleting profile id {profileId} at path {profilePath}");
        GodotFileIo fileIo = new(profilePath);
        fileIo.DeleteDirectoryRecursive(fileIo.GetFullPath(""));
    }

    public void SaveSettings()
    {
        _settingsSaveManager.SaveSettings();
    }

    public void SaveProfile()
    {
        _profileSaveManager.SaveProfile();
    }

    /// <summary>
    /// Initializes a default settings file for testing purposes.
    /// This is separate from InitSettingsData because that is used directly in tests.
    /// </summary>
    public ReadSaveResult<SettingsSave> InitSettingsDataForTest()
    {
        _settingsSaveManager.Settings = new SettingsSave();
        return new ReadSaveResult<SettingsSave>(_settingsSaveManager.Settings);
    }

    /// <summary>
    /// Initializes a default prefs file for testing purposes.
    /// This is separate from InitPrefsData because that is used directly in tests.
    /// </summary>
    public ReadSaveResult<PrefsSave> InitPrefsDataForTest()
    {
        _prefsSaveManager.Prefs = new PrefsSave();
        return new ReadSaveResult<PrefsSave>(_prefsSaveManager.Prefs);
    }

    /// <summary>
    /// Loads the settings file for the first time. This should only be called once early on in the lifetime of the game.
    /// If the settings save could not be read because it was corrupt or did not exist, a new one is created.
    /// </summary>
    /// <returns>The result of reading the settings save file.</returns>
    public ReadSaveResult<SettingsSave> InitSettingsData()
    {
        return _settingsSaveManager.LoadSettings();
    }

    /// <summary>
    /// Loads the prefs file for the first time.
    /// This should be called once early on in the lifetime of the game, and then only when the player switches profiles
    /// after that. If the progress save could not be read because it was corrupt or did not exist, a new one is created.
    /// </summary>
    /// <returns>The result of reading the prefs save file.</returns>
    public ReadSaveResult<PrefsSave> InitPrefsData()
    {
        return _prefsSaveManager.LoadPrefs();
    }

    /// <summary>
    /// Loads the progress file for the first time.
    /// This should be called once early on in the lifetime of the game, and then only when the player switches profiles
    /// after that. If the progress save could not be read because it was corrupt or did not exist, a new one is created.
    /// </summary>
    /// <returns>The result of reading the progress save file.</returns>
    public ReadSaveResult<ProgressSave> InitProgressData()
    {
        return _progressSaveManager.LoadProgress();
    }

    /// <summary>
    /// Load the current climb save.
    /// </summary>
    /// <returns>The current climb save, wrapped in a result object that contains some status info.</returns>
    public ReadSaveResult<SerializableClimb> LoadClimbSave() => _climbSaveManager.LoadClimbSave();

    /// <summary>
    /// Load the current multiplayer climb save.
    /// </summary>
    /// <returns>The current multiplayer climb save, wrapped in a result object that contains some status info.</returns>
    public ReadSaveResult<SerializableClimb> LoadMultiplayerClimbSave() => _climbSaveManager.LoadMultiplayerClimbSave();

    /// <summary>
    /// Load and validate the current multiplayer climb save.
    /// Performs deep validation to ensure all save content is valid and handles corruption automatically.
    /// </summary>
    /// <param name="localPlayerId">The local player ID to validate against</param>
    /// <returns>The current multiplayer climb save with validation status</returns>
    public ReadSaveResult<SerializableClimb> LoadAndValidateMultiplayerClimbSave(ulong localPlayerId) => _climbSaveManager.LoadAndValidateMultiplayerClimbSave(localPlayerId);

    public void SaveClimbHistory(ClimbHistory history)
    {
        _climbHistorySaveManager.SaveHistory(history);
    }

    public int GetClimbHistoryCount()
    {
        return _climbHistorySaveManager.GetHistoryCount();
    }

    public List<string> GetAllClimbHistoryNames()
    {
        return _climbHistorySaveManager.LoadAllClimbHistoryNames();
    }

    public ReadSaveResult<ClimbHistory> LoadClimbHistory(string fileName)
    {
        return _climbHistorySaveManager.LoadHistory(fileName);
    }

    public static string ToJson<T>(T obj, JsonSerializerOptions? options = null) where T : ISaveSchema
    {
        return JsonSerializationUtility.ToJson(obj, options);
    }

    public static ReadSaveResult<T> FromJson<T>(string json) where T : ISaveSchema, new()
    {
        return JsonSerializationUtility.FromJson<T>(json);
    }

    /// <summary>
    /// Returns true if all ftues are disabled OR if the given ftue key exists (seen by the player before).
    /// Is also disabled if there's no game (test mode)
    /// </summary>
    /// <param name="ftueKey"></param>
    /// <returns></returns>
    public bool SeenFtue(string ftueKey) => _progressSaveManager.SeenFtue(ftueKey);

    public void SaveProgressFile()
    {
        _progressSaveManager.SaveProgress();
    }

    /// <summary>
    /// Set the prefs file from another source.
    /// This should only be used in the settings save migrator.
    /// </summary>
    public void SetPrefsFile(PrefsSave save)
    {
        _prefsSaveManager.Prefs = save;
    }

    public void SavePrefsFile()
    {
        _prefsSaveManager.SavePrefs();
    }

    public void MarkFtueAsComplete(string ftueId)
    {
        _progressSaveManager.MarkFtueAsComplete(ftueId);
    }

    public void SetFtuesEnabled(bool enabled)
    {
        _progressSaveManager.SetFtuesEnabled(enabled);
    }

    public void ResetFtues()
    {
        _progressSaveManager.ResetFtues();
    }

    public void MarkPotionAsSeen(PotionModel potion)
    {
        _progressSaveManager.MarkPotionAsSeen(potion);
    }

    public void MarkCardAsSeen(CardModel card)
    {
        _progressSaveManager.MarkCardAsSeen(card);
    }

    public void MarkRelicAsSeen(RelicModel relic)
    {
        _progressSaveManager.MarkRelicAsSeen(relic);
    }

    public bool IsRelicSeen(RelicModel relic)
    {
        return ProgressSave.DiscoveredRelics.Contains(relic.Id);
    }

    #region Epochs

    /// <summary>
    /// Sets an Epoch Slot to be available but not revealed/obtained.
    /// </summary>
    public void UnlockSlot(string epochId)
    {
        SerializableEpoch? epoch = ProgressSave.Epochs.FirstOrDefault(epoch => epoch.Id == epochId);

        // If the slot doesn't exist yet, create a new Epoch entry in our Epochs list. Hooray slots
        if (epoch == null)
        {
            ProgressSave.Epochs.Add(new SerializableEpoch(epochId, EpochState.NotObtained));
        }
        else
        {
            if (epoch.State == EpochState.ObtainedNoSlot)
            {
                Log.Info($"Attempted to get slot {epochId} but we already have an Epoch! Set to Obtained");
                epoch.State = EpochState.Obtained;
            }
            else
            {
                Log.Error($"Slot unlocked for {epochId} but it's in an invalid state: {epoch.State}");
            }
        }
    }

    /// <summary>
    /// Sets or creates an Epoch to any EpochState we wish.
    /// Used by TimelineExpansions for overriding behaviors.
    /// </summary>
    public void ObtainEpoch(string epochId) => _progressSaveManager.ObtainEpoch(epochId);

    /// <summary>
    /// Sets or creates an Epoch to any EpochState we wish.
    /// Used by TimelineExpansions for overriding behaviors.
    /// </summary>
    public void ObtainEpochOverride(string epochId, EpochState state) => _progressSaveManager.ObtainEpochOverride(epochId, state);

    /// <summary>
    /// Reveals an Epoch. Sets an Epoch to IsComplete.
    /// Occurs when the player clicks on an Obtained Epoch in the Timeline screen.
    /// We automatically save for now.
    /// </summary>
    public void RevealEpoch(string epochId)
    {
        SerializableEpoch? epoch = ProgressSave.Epochs.FirstOrDefault(epoch => epoch.Id == epochId);
        if (epoch == null) throw new InvalidOperationException($"Invalid epoch {epochId} passed to {nameof(RevealEpoch)}!");
        epoch.State = EpochState.Revealed;
        MetricUtilities.UploadEpochMetric(epoch.Id);
        UnlockState.UnlockEpoch(epochId);
    }

    /// <summary>
    /// Called by the debug Reset Progress button in the Timeline screen.
    /// </summary>
    public void ResetTimelineProgress()
    {
        ProgressSave.Epochs.Clear();

        // This is only ever called via the Reset Progress button on the Timeline screen, so we know we can pass null
        // for the local player.
        ObtainEpochOverride(EpochModel.GetId<NeowEpoch>(), EpochState.Obtained);

        SaveProgressFile();
    }

    /// <summary>
    /// Checks if an epoch has been revealed on the timeline.
    /// You should only be querying this from the main menu. When you are inside of a climb, you should be using
    /// ClimbState.UnlockState or Player.UnlockState.
    /// </summary>
    public bool IsEpochRevealed<T>() where T : EpochModel => _progressSaveManager.IsEpochRevealed<T>();

    /// <summary>
    /// Checks if an epoch has been revealed on the timeline.
    /// You should only be querying this from the main menu. When you are inside of a climb, you should be using
    /// ClimbState.UnlockState or Player.UnlockState.
    /// </summary>
    public bool IsEpochRevealed(string id) => _progressSaveManager.HasEpoch(id);

    #endregion

    public int GetTotalUnlockedCards()
    {
        return GetCardUnlockEpochIds().Count(IsEpochRevealed) * 3;
    }

    public static int GetUnlockableCardCount()
    {
        return GetCardUnlockEpochIds().Length * 3;
    }

    /// <summary>
    /// Helper method which returns every Epoch that unlocks cards in the game.
    /// Modify this list to affect our total card unlock statistics.
    /// </summary>
    private static string[] GetCardUnlockEpochIds()
    {
        string[] epochs =
        [
            EpochModel.GetId<Colorless1Epoch>(),
            EpochModel.GetId<Colorless2Epoch>(),
            EpochModel.GetId<Colorless3Epoch>(),
            EpochModel.GetId<Colorless4Epoch>(),
            EpochModel.GetId<Colorless5Epoch>(),
            EpochModel.GetId<Ironclad2Epoch>(),
            EpochModel.GetId<Ironclad5Epoch>(),
            EpochModel.GetId<Ironclad7Epoch>(),
            EpochModel.GetId<Silent2Epoch>(),
            EpochModel.GetId<Silent5Epoch>(),
            EpochModel.GetId<Silent7Epoch>(),
            EpochModel.GetId<Regent2Epoch>(),
            EpochModel.GetId<Regent5Epoch>(),
            EpochModel.GetId<Regent7Epoch>(),
            EpochModel.GetId<Defect2Epoch>(),
            EpochModel.GetId<Defect5Epoch>(),
            EpochModel.GetId<Defect7Epoch>(),
            EpochModel.GetId<Necrobinder2Epoch>(),
            EpochModel.GetId<Necrobinder5Epoch>(),
            EpochModel.GetId<Necrobinder7Epoch>()
            // EpochModel.GetId<WatcherUnlock2Epoch>(),
            // EpochModel.GetId<WatcherUnlock5Epoch>(),
            // EpochModel.GetId<WatcherUnlock7Epoch>(),
        ];

        return epochs;
    }

    public int GetTotalUnlockedRelics()
    {
        return GetRelicUnlockEpochIds().Count(IsEpochRevealed) * 3;
    }

    public static int GetUnlockableRelicCount()
    {
        return GetRelicUnlockEpochIds().Length * 3;
    }

    /// <summary>
    /// Helper method which returns every Epoch that unlocks relics in the game.
    /// Modify this list to affect our total relic unlock statistics.
    /// </summary>
    private static string[] GetRelicUnlockEpochIds()
    {
        string[] epochs =
        [
            EpochModel.GetId<Relic1Epoch>(),
            EpochModel.GetId<Relic2Epoch>(),
            EpochModel.GetId<Relic3Epoch>(),
            EpochModel.GetId<Relic4Epoch>(),
            EpochModel.GetId<Relic5Epoch>(),
            EpochModel.GetId<Ironclad3Epoch>(),
            EpochModel.GetId<Ironclad6Epoch>(),
            EpochModel.GetId<Silent3Epoch>(),
            EpochModel.GetId<Silent6Epoch>(),
            EpochModel.GetId<Regent3Epoch>(),
            EpochModel.GetId<Regent6Epoch>(),
            EpochModel.GetId<Defect3Epoch>(),
            EpochModel.GetId<Defect6Epoch>(),
            EpochModel.GetId<Necrobinder3Epoch>(),
            EpochModel.GetId<Necrobinder6Epoch>()
            // EpochModel.GetId<WatcherUnlock3Epoch>(),
            // EpochModel.GetId<WatcherUnlock6Epoch>(),
        ];

        return epochs;
    }

    public int GetTotalUnlockedPotions()
    {
        return GetPotionUnlockEpochIds().Count(IsEpochRevealed) * 3;
    }

    public static int GetUnlockablePotionCount()
    {
        return GetPotionUnlockEpochIds().Length * 3;
    }

    /// <summary>
    /// Helper method which returns every Epoch that unlocks relics in the game.
    /// Modify this list to affect our total relic unlock statistics.
    /// </summary>
    private static string[] GetPotionUnlockEpochIds()
    {
        string[] epochs =
        [
            EpochModel.GetId<Potion1Epoch>(),
            EpochModel.GetId<Potion2Epoch>(),
            EpochModel.GetId<Ironclad4Epoch>(),
            EpochModel.GetId<Silent4Epoch>(),
            EpochModel.GetId<Regent4Epoch>(),
            EpochModel.GetId<Defect4Epoch>(),
            EpochModel.GetId<Necrobinder4Epoch>()
            // EpochModel.GetId<WatcherUnlock4Epoch>(),
        ];

        return epochs;
    }

    /// <summary>
    /// Returns the sum of the Ascension progress of every character the player has.
    /// </summary>
    public int GetAggregateAscensionProgress()
    {
        return ProgressSave.CharStats.Sum(stat => stat.MaxAscension);
    }

    public static int GetAggregateAscensionCount() => ModelDb.AllCharacters.Count() * AscensionManager.maxAscensionAllowed;

    public int GetTotalKills()
    {
        return ProgressSave.EnemyStats.Sum(enemy => enemy.TotalWins);
    }

    public int GetObtainedEpochCount()
    {
        return ProgressSave.Epochs.Count(epoch => epoch.State == EpochState.Obtained);
    }

    public bool IsNeowObtainedButNotRevealed()
    {
        SerializableEpoch? epoch = ProgressSave.Epochs
            .FirstOrDefault(e => e.Id == EpochModel.GetId<NeowEpoch>());

        if (epoch == null)
        {
            return false;
        }

        return epoch.State != EpochState.Revealed;
    }
}
