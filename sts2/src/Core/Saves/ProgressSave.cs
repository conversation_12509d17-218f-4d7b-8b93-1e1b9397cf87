using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Saves;

/// <summary>
/// The player's gameplay progress for Slay the Spire 2.
/// This would be considered the most important save file and is synced across devices/platforms.
/// There would be a progress.save in each respective profile's folder and contains data such as:
/// - Total wins, losses, playtime, and progress per character
/// - Which cards/relics/potions/etc have been seen or unlocked
/// - Which enemies/elites/bosses have been encountered and defeated
/// - Character data
/// - Timeline & Unlock progress (meta progression)
/// - Unlocked achievements
/// - Which FTUEs this player has seen
/// </summary>
public class ProgressSave : ISaveSchema
{
    public ProgressSave()
    {
        UniqueId = GenerateUniqueId();
        return;

        // Used to identify duplicate tester data anonymously.
        // Generates a 7 character length ID using alphanumeric chars.
        // 64 billion combinations are possible.
        string GenerateUniqueId(int length = 7)
        {
            const string characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            return new string(Enumerable.Repeat(characters, length)
                .Select(s => s[Rng.Chaotic.NextInt(s.Length)]).ToArray());
        }
    }

    /// <summary>
    /// The schema version of this save.
    /// </summary>
    [JsonPropertyName("schema_version")]
    public int SchemaVersion { get; set; }

    [JsonPropertyName("unique_id")]
    public string UniqueId { get; init; }

    [JsonPropertyName("character_stats")]
    public List<CharacterStats> CharStats { get; set; } = [];

    [JsonPropertyName("card_stats")]
    public List<CardStats> CardStats { get; set; } = [];

    /// <summary>
    /// Tracks how often each character has encountered, won, and lost against each Encounter.
    /// </summary>
    [JsonPropertyName("encounter_stats")]
    public List<EncounterStats> EncounterStats { get; set; } = [];

    /// <summary>
    /// Tracks how often each character has encountered, won, and lost against each Enemy Creature.
    /// </summary>
    [JsonPropertyName("enemy_stats")]
    public List<EnemyStats> EnemyStats { get; set; } = [];

    /// <summary>
    /// Tracks how often each character has encountered, won, and lost after encountering each ancient.
    /// </summary>
    [JsonPropertyName("ancient_stats")]
    public List<AncientStats> AncientStats { get; set; } = [];

    [JsonPropertyName("enable_ftues")]
    public bool EnableFtues { get; set; } = true;

    [JsonPropertyName("epochs")]
    public List<SerializableEpoch> Epochs { get; set; } = [];

    [JsonPropertyName("ftue_completed")]
    public List<string> FtueCompleted { get; set; } = [];

    [JsonPropertyName("achievement_metadata")]
    public List<SerializableUnlockedAchievement> UnlockedAchievements { get; set; } = [];

    [JsonPropertyName("discovered_cards")]
    public List<ModelId> DiscoveredCards { get; set; } = [];

    [JsonPropertyName("discovered_relics")]
    public List<ModelId> DiscoveredRelics { get; set; } = [];

    [JsonPropertyName("discovered_events")]
    public List<ModelId> DiscoveredEvents { get; set; } = [];

    [JsonPropertyName("discovered_potions")]
    public List<ModelId> DiscoveredPotions { get; set; } = [];

    [JsonPropertyName("total_playtime")]
    public long TotalPlaytime { get; set; } // NOTE: In seconds

    [JsonPropertyName("floors_climbed")]
    public long FloorsClimbed { get; set; }

    // See Welcome to Wongo's event
    [JsonPropertyName("wongo_points")]
    public int WongoPoints { get; set; }

    /// <summary> Which multiplayer ascension we had selected the last time we started a multiplayer run. </summary>
    [JsonPropertyName("preferred_multiplayer_ascension")]
    public int PreferredMultiplayerAscension { get; set; }

    /// <summary>
    /// The maximum unlocked ascension in multiplayer.
    /// In singleplayer climbs, we use character stats. In multiplayer climbs, all characters share an ascension.
    /// </summary>
    [JsonPropertyName("max_multiplayer_ascension")]
    public int MaxMultiplayerAscension { get; set; }

    // See Test Subject combat
    [JsonPropertyName("test_subject_kills")]
    public int TestSubjectKills { get; set; }

    [JsonIgnore]
    public int Wins => CharStats.Sum(character => character.TotalWins);

    [JsonIgnore]
    public int Losses => CharStats.Sum(character => character.TotalLosses);

    [JsonIgnore]
    public long FastestVictory
    {
        get
        {
            if (CharStats.Count == 0) return Really.bigNumber;

            return CharStats.Min(c => c.FastestWinTime == -1 ? Really.bigNumber : c.FastestWinTime);
        }
    }

    [JsonIgnore]
    public long BestWinStreak
    {
        get
        {
            if (CharStats.Count == 0) return 0;

            return CharStats.Max(c => c.BestWinStreak);
        }
    }

    [JsonIgnore]
    public int NumberOfClimbs => Wins + Losses;
}
