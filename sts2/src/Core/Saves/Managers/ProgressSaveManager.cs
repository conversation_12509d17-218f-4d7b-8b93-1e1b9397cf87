using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Acts;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Events;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.Saves.Migrations;
using MegaCrit.Sts2.Core.Timeline;
using MegaCrit.Sts2.Core.Timeline.Epochs;
using MegaCrit.Sts2.Core.Unlocks;

namespace MegaCrit.Sts2.Core.Saves.Managers;

public class ProgressSaveManager
{
    public const string fileName = "progress.save";
    private readonly ISaveStore _saveStore;
    private readonly MigrationManager _migrationManager;
    private string ProgressPath => _saveStore.GetFullPath(Path.Combine(UserDataPathProvider.GetProfileDir(CurrentProfileId), UserDataPathProvider.SavesDir, fileName));
    public int CurrentProfileId { get; set; }

    public ProgressSave Progress { get; set; } = new();
    public UnlockState UnlockState { get; set; } = UnlockState.none;

    public ProgressSaveManager(int profileId, ISaveStore saveStore, MigrationManager migrationManager)
    {
        _saveStore = saveStore;
        _migrationManager = migrationManager;
        CurrentProfileId = profileId;
    }

    public void SaveProgress()
    {
        Progress.SchemaVersion = _migrationManager.GetLatestVersion<ProgressSave>();
        string json = JsonSerializationUtility.ToJson(Progress);
        _saveStore.WriteFile(ProgressPath, json);
    }

    public ReadSaveResult<ProgressSave> LoadProgress()
    {
        ReadSaveResult<ProgressSave> result = _migrationManager.LoadSave<ProgressSave>(ProgressPath);

        // Silent auto-recovery: create new save if load failed for any reason
        if (!result.Success || result.SaveData == null)
        {
            // MigrationManager already handled any corruption during LoadSave

            Progress = _migrationManager.CreateNewSave<ProgressSave>();

            // Apply starter content for new save
            Ironclad ironclad = ModelDb.Character<Ironclad>();
            foreach (CardModel card in ironclad.StartingDeck)
            {
                MarkCardAsSeen(card);
            }

            foreach (RelicModel relic in ironclad.StartingRelics)
            {
                MarkRelicAsSeen(relic);
            }

            // Save the progress file with the marked cards/relics
            SaveProgress();
        }
        else
        {
            Progress = result.SaveData;
        }

        UnlockState = new UnlockState(Progress);

        return result;
    }


    public CharacterStats GetCharacterStats(ModelId characterId)
    {
        foreach (CharacterStats stats in Progress.CharStats)
        {
            if (stats.Id == characterId)
            {
                return stats;
            }
        }

        CharacterStats retVal = new()
        {
            Id = characterId
        };
        Progress.CharStats.Add(retVal);

        return retVal;
    }

    public CardStats GetCardStats(ModelId cardId)
    {
        foreach (CardStats stats in Progress.CardStats)
        {
            if (stats.Id == cardId)
            {
                return stats;
            }
        }

        CardStats retVal = new()
        {
            Id = cardId
        };
        Progress.CardStats.Add(retVal);

        return retVal;
    }

    public EncounterStats? GetEncounterStats(ModelId encounterId)
    {
        return Progress.EncounterStats.FirstOrDefault(encounter => encounter.Id == encounterId);
    }

    /// <summary>
    /// Called after a loss. Marks the player as having lost to an encounter (not a monster).
    /// </summary>
    private void IncrementEncounterLoss(ModelId characterId, int ascension, ModelId encounterId)
    {
        EncounterStats? encounterStat = GetEncounterStats(encounterId);

        // This encounter has no log, create a new EncounterStats AND FightStats entry
        if (encounterStat == null)
        {
            Log.Info($"{characterId} fought {encounterId} for the first time and LOST :(");
            EncounterStats newStat = new()
            {
                Id = encounterId,
                FightStats = []
            };

            FightStats fightEntry = new()
            {
                Character = characterId,
                Wins = 0,
                Losses = 1,
                MaxAscension = ascension
            };

            newStat.FightStats.Add(fightEntry);
            Progress.EncounterStats.Add(newStat);
        }
        else
        {
            FightStats? fight = encounterStat.FightStats.FirstOrDefault(fight => fight.Character == characterId);

            if (fight == null)
            {
                Log.Info($"While you have fought this encounter before, it's the first time {characterId} is fighting it!");

                FightStats fightEntry = new()
                {
                    Character = characterId,
                    Wins = 0,
                    Losses = 1,
                    MaxAscension = ascension
                };

                encounterStat.FightStats.Add(fightEntry);
            }
            else
            {
                // Character/Encounter combo has been logged before. Increase how many times they've lost to this encounter by 1.
                encounterStat.IncrementLoss(characterId);
            }
        }
    }

    /// <summary>
    /// Called after a loss. Marks the player as having lost to a specific monster in an encounter.
    /// </summary>
    private void IncrementEnemyFightLoss(ModelId characterId, int ascension, ModelId monster)
    {
        EnemyStats? enemyStat = Progress.EnemyStats.FirstOrDefault(enemy => enemy.Id == monster);

        if (enemyStat == null)
        {
            Log.Info($"{characterId} fought {monster} for the first time and LOST >:(");
            EnemyStats newStat = new()
            {
                Id = monster,
                FightStats = []
            };

            FightStats fightEntry = new()
            {
                Character = characterId,
                Wins = 0,
                Losses = 1,
                MaxAscension = ascension
            };

            newStat.FightStats.Add(fightEntry);
            Progress.EnemyStats.Add(newStat);
        }
        // Player has fought this monster before
        else
        {
            FightStats? fight = enemyStat.FightStats.FirstOrDefault(fight => fight.Character == characterId);

            // This character has never fought this monster before
            if (fight == null)
            {
                FightStats fightEntry = new()
                {
                    Character = characterId,
                    Wins = 0,
                    Losses = 1,
                    MaxAscension = ascension
                };

                enemyStat.FightStats.Add(fightEntry);
            }
            else
            {
                enemyStat.IncrementLoss(characterId);
            }
        }
    }

    public void MarkPotionAsSeen(PotionModel potion)
    {
        if (Progress.DiscoveredPotions.Contains(potion.Id)) return;

        Progress.DiscoveredPotions.Add(potion.Id);

        // If we discover a potion, save it for the DISCOVERIES section in the game over screen
        if (LocalContext.IsMine(potion))
        {
            potion.Owner.DiscoveredPotions++;
        }
    }

    public void MarkCardAsSeen(CardModel card)
    {
        if (Progress.DiscoveredCards.Contains(card.Id)) return;

        Progress.DiscoveredCards.Add(card.Id);

        // If we discover a card, save it for the DISCOVERIES section in the game over screen
        if (LocalContext.IsMine(card))
        {
            card.Owner.DiscoveredCards++;
        }
    }

    public void MarkRelicAsSeen(RelicModel relic)
    {
        if (Progress.DiscoveredRelics.Contains(relic.Id)) return;

        Progress.DiscoveredRelics.Add(relic.Id);

        // If we discover a relic, save it for the DISCOVERIES section in the game over screen
        if (LocalContext.IsMine(relic))
        {
            relic.Owner.DiscoveredRelics++;
        }
    }

    /// <summary>
    /// Called whenever a Climb is completed. This could be Death, Victory, True Victory (not yet implemented),
    /// and even Abandon Climb in the main menu.
    /// </summary>
    public void UpdateWithClimbData(SerializableClimb serializableClimb, bool victory)
    {
        SerializablePlayer? localPlayer;
        bool isSingleplayer = serializableClimb.Players.Count == 1;

        // If there is only 1 player, that means this was single player climb
        if (isSingleplayer)
        {
            localPlayer = serializableClimb.Players.First();
        }
        else
        {
            // Multiplayer
            ulong playerId = PlatformUtil.GetLocalPlayerId(serializableClimb.PlatformType);
            localPlayer = serializableClimb.Players.FirstOrDefault(p => p.NetId == playerId);
            if (localPlayer == null)
            {
                Log.Warn($"Local player with net id {playerId} not found in climb! Progress will not be updated");
                return;
            }
        }

        for (int i = 0; i < serializableClimb.MapPointHistory.Count; i++)
        {
            // This can happen in tests with climbs that aren't well-constructed
            if (i >= serializableClimb.Acts.Count)
            {
                Log.Warn($"There are {serializableClimb.MapPointHistory.Count} acts in the map point history, but {serializableClimb.Acts.Count} acts in the act array! This is unexpected");
                continue;
            }

            // The player only visited the acts in the climb that they have map point histories for
            ModelId actId = serializableClimb.Acts[i].Id!;

            if (!Progress.DiscoveredActs.Contains(actId))
            {
                Progress.DiscoveredActs.Add(actId);
            }
        }

        List<MapPointHistoryEntry> mapPoints = serializableClimb.MapPointHistory.SelectMany(act => act).ToList();

        Progress.TotalPlaytime += serializableClimb.WinTime > 0 ? serializableClimb.WinTime : serializableClimb.ClimbTime;
        Progress.WongoPoints += localPlayer.ExtraFields.WongoPoints;
        Progress.TestSubjectKills += serializableClimb.ExtraFields.TestSubjectKills;
        Progress.FloorsClimbed += mapPoints.Count;
        CharacterStats characterStats = GetCharacterStats(localPlayer.CharacterId!);
        characterStats.Playtime += serializableClimb.WinTime > 0 ? serializableClimb.WinTime : serializableClimb.ClimbTime;

        if (victory)
        {
            if (isSingleplayer)
            {
                IncrementSingleplayerAscension(serializableClimb, characterStats);
            }
            else
            {
                IncrementMultiplayerAscension(serializableClimb);
            }

            characterStats.TotalWins++;
            characterStats.CurrentWinStreak++;
            characterStats.BestWinStreak = Math.Max(characterStats.BestWinStreak, characterStats.CurrentWinStreak);

            if (characterStats.FastestWinTime < 0 || characterStats.FastestWinTime > serializableClimb.ClimbTime)
            {
                characterStats.FastestWinTime = serializableClimb.ClimbTime;
            }
        }
        else
        {
            characterStats.CurrentWinStreak = 0;
            characterStats.TotalLosses++;

            // If the player was killed or gave up in an encounter, we update ProgressSave.EncounterStats
            MapPointHistoryEntry? lastEntry = serializableClimb.MapPointHistory.LastOrDefault()?.LastOrDefault();

            if (lastEntry != null)
            {
                RoomType roomType = lastEntry.Rooms.Last().RoomType;

                if (roomType.IsCombatRoom())
                {
                    ModelId killedByEncounter = lastEntry.Rooms.Last().ModelId!;
                    ModelId character = localPlayer.CharacterId!;
                    IncrementEncounterLoss(character, serializableClimb.Ascension, killedByEncounter);

                    foreach (ModelId monster in lastEntry.Rooms.Last().MonsterIds)
                    {
                        IncrementEnemyFightLoss(character, serializableClimb.Ascension, monster);
                    }
                }
            }
        }

        foreach (SerializableCard cardModelSave in localPlayer.Deck)
        {
            CardStats cardStats = GetCardStats(cardModelSave.Id!);
            if (victory)
            {
                cardStats.TimesWon++;
            }
            else
            {
                cardStats.TimesLost++;
            }
        }

        foreach (MapPointHistoryEntry entry in mapPoints)
        {
            MapPointRoomHistoryEntry? eventRoom = entry.FirstRoomOfType(RoomType.Event);
            if (eventRoom != null && !Progress.DiscoveredEvents.Contains(eventRoom.ModelId!))
            {
                Progress.DiscoveredEvents.Add(eventRoom.ModelId!);
            }

            PlayerMapPointHistoryEntry playerEntry = entry.GetEntry(localPlayer.NetId);

            foreach (ModelChoiceHistoryEntry cardChoice in playerEntry.CardChoices)
            {
                CardStats cardStats = GetCardStats(cardChoice.choice);

                if (cardChoice.wasPicked)
                {
                    cardStats.TimesPicked++;
                }
                else
                {
                    cardStats.TimesSkipped++;
                }
            }

            if (entry.MapPointType == MapPointType.Ancient)
            {
                MapPointRoomHistoryEntry ancientRoom = entry.FirstRoomOfType(RoomType.Event)!;
                AncientStats? stats = Progress.AncientStats.FirstOrDefault(a => a.Id == ancientRoom.ModelId);

                if (stats == null)
                {
                    stats = new AncientStats { Id = ancientRoom.ModelId! };
                    Progress.AncientStats.Add(stats);
                }

                if (victory)
                {
                    stats.IncrementWin(localPlayer.CharacterId!);
                }
                else
                {
                    stats.IncrementLoss(localPlayer.CharacterId!);
                }
            }
        }

        // Call this late so that all progress has been updated
        UpdateEpochsPostClimb(localPlayer, serializableClimb.Ascension, victory);

        SaveProgress();
    }

    #region Epochs

    /// <summary>
    /// Check for Epoch-related unlocks at the end of each Climb.
    /// Singleplayer only.
    /// </summary>
    private void UpdateEpochsPostClimb(SerializablePlayer localPlayer, int ascension, bool victory)
    {
        // My first Epoch!
        string neowEpochId = EpochModel.GetId<NeowEpoch>();
        if (!IsEpochObtained(neowEpochId))
        {
            Progress.Epochs.Add(new SerializableEpoch(neowEpochId, EpochState.Obtained));
            NGame.Instance?.MainMenu?.RefreshButtons();
            NGame.Instance?.AddChildSafely(NGainEpochVfx.Create(EpochModel.Get(neowEpochId)));
        }

        PostClimbUnlockCharacterEpochCheck(localPlayer.CharacterId!);
        PostClimbCharacterEpochChecks(localPlayer.CharacterId!, ascension, victory);

        // Unlock Ascension and Daily Climb if you beat the game on Ascension 0
        if (victory && ascension == 0)
        {
            // If we beat Act 3, AscensionEpoch is obtained
            TryObtainEpoch(EpochModel.Get<DailyClimbEpoch>(), null);
        }

        int characterCount = ModelDb.AllCharacters.Count();

        // Unlock the Compendium (Card Library, Potion Lab, Relic Collection, and Bestiary)
        // And unlock Orobas (Ancient with cross-color options)
        // if you have played the game once with each unique character.
        if (Progress.CharStats.Count >= characterCount)
        {
            TryObtainEpoch(EpochModel.Get<CompendiumEpoch>(), null);
            TryObtainEpoch(EpochModel.Get<OrobasEpoch>(), null);
        }

        // Unlock Darv if we've encountered every ancient other than Darv.
        HashSet<ModelId> ancientsEncountered = Progress.AncientStats.Select(a => a.Id).ToHashSet();
        if (ModelDb.AllAncients.All(a => ancientsEncountered.Contains(a.Id) || a is Darv))
        {
            TryObtainEpoch(EpochModel.Get<DarvEpoch>(), null);
        }

        // Unlock Zlatir if we've hit ten losses.
        if (Progress.Losses >= 10)
        {
            TryObtainEpoch(EpochModel.Get<ZlatirEpoch>(), null);
        }
    }

    /// <summary>
    /// If you complete a run with a character for the first time,
    /// there's generally an unlock for another character.
    /// </summary>
    private void PostClimbUnlockCharacterEpochCheck(ModelId characterId)
    {
        string? epochId = ModelDb.GetById<CharacterModel>(characterId) switch
        {
            Silent => EpochModel.GetId<Regent1Epoch>(),
            Regent => EpochModel.GetId<Defect1Epoch>(),
            Defect => EpochModel.GetId<Necrobinder1Epoch>(),
            _ => null
        };

        // Since this method is called post-climb, there is no local player, so we pass null here.
        if (epochId != null)
        {
            TryObtainEpoch(EpochModel.Get(epochId), null);
        }
    }

    private void PostClimbCharacterEpochChecks(ModelId characterId, int ascension, bool victory)
    {
        if (!victory) return;

        // Check for Character Epoch 7
        CheckAscensionOneCompleted(characterId, ascension);

        // Check for Custom Climb and Seeds Epoch
        string epochId = EpochModel.GetId<CustomAndSeedsEpoch>();

        if (Progress.NumberOfClimbs >= 3)
        {
            TryObtainEpoch(EpochModel.Get(epochId), null);
        }
    }

    /// <summary>
    /// When the player completes Ascension 1 they unlock Character Epoch 7.
    /// This unlock grants the player the means to Kill the Architect and 3 cards.
    /// </summary>
    /// <param name="characterId"></param>
    /// <param name="ascension"></param>
    private void CheckAscensionOneCompleted(ModelId characterId, int ascension)
    {
        if (ascension != 1) return;

        string? epochId = ModelDb.GetById<CharacterModel>(characterId) switch
        {
            Ironclad => EpochModel.GetId<Ironclad7Epoch>(),
            Silent => EpochModel.GetId<Silent7Epoch>(),
            Regent => EpochModel.GetId<Regent7Epoch>(),
            Defect => EpochModel.GetId<Defect7Epoch>(),
            Necrobinder => EpochModel.GetId<Necrobinder7Epoch>(),
            _ => null
        };

        // Since this method is called post-climb, there is no local player, so we pass null here and below.
        if (epochId != null)
        {
            TryObtainEpoch(EpochModel.Get(epochId), null);
        }
    }

    private void CheckTenBossesDefeatedEpoch(Player localPlayer)
    {
        CharacterModel character = localPlayer.Character;
        EpochModel? epoch = character switch
        {
            Ironclad => EpochModel.Get(EpochModel.GetId<Ironclad6Epoch>()),
            Silent => EpochModel.Get(EpochModel.GetId<Silent6Epoch>()),
            Regent => EpochModel.Get(EpochModel.GetId<Regent6Epoch>()),
            Defect => EpochModel.Get(EpochModel.GetId<Defect6Epoch>()),
            Necrobinder => EpochModel.Get(EpochModel.GetId<Necrobinder6Epoch>()),
            Deprived => null,
            // Watcher?
            _ => throw new ArgumentOutOfRangeException(nameof(character), character, null)
        };

        // Checks if we defeated 10 bosses with the character we're playing as.
        bool defeatedTen = false;
        foreach (EncounterStats encounter in Progress.EncounterStats)
        {
            foreach (FightStats fightStat in encounter.FightStats)
            {
                if (fightStat.Character == character.Id && fightStat.Wins >= 10)
                {
                    defeatedTen = true;
                    break;
                }
            }
        }

        // If we defeated 10 or more bosses AND we still don't have this Epoch, get a new Epoch yay!
        if (defeatedTen && epoch != null)
        {
            TryObtainEpoch(epoch, localPlayer);
        }
    }

    private void CheckUnderdocksEpoch(Player localPlayer)
    {
        // Get all encounters we've ever won
        HashSet<ModelId> encountersWon = Progress.EncounterStats
            .Where(e => e.FightStats.Any(f => f.Wins > 0))
            .Select(e => e.Id).ToHashSet();

        // Get all the overgrowth bosses we've defeated
        int overgrowthBossesCount = ModelDb.Act<Overgrowth>().AllBossEncounters.Count();
        int overgrowthBossesDefeatedCount = ModelDb.Act<Overgrowth>().AllBossEncounters
            .Count(e => encountersWon.Contains(e.Id));

        EpochModel epoch = EpochModel.Get<UnderdocksEpoch>();

        // If we defeated all 3 overgrowth bosses AND we still don't have this Epoch, get a new Epoch yay!
        if (overgrowthBossesDefeatedCount >= overgrowthBossesCount)
        {
            TryObtainEpoch(epoch, localPlayer);
        }
    }

    /// <summary>
    /// Checks for Character5Epoch for all characters.
    /// Requires defeating every Elite in the game with the character.
    /// </summary>
    private void CheckAllElitesDefeatedEpoch(Player localPlayer)
    {
        CharacterModel character = localPlayer.Character;
        EpochModel? epoch = character switch
        {
            Ironclad => EpochModel.Get(EpochModel.GetId<Ironclad5Epoch>()),
            Silent => EpochModel.Get(EpochModel.GetId<Silent5Epoch>()),
            Regent => EpochModel.Get(EpochModel.GetId<Regent5Epoch>()),
            Defect => EpochModel.Get(EpochModel.GetId<Defect5Epoch>()),
            Necrobinder => EpochModel.Get(EpochModel.GetId<Necrobinder5Epoch>()),
            Deprived => null,
            // Watcher?
            _ => throw new ArgumentOutOfRangeException(nameof(character), character, null)
        };

        // Checks how many unique elites this character has defeated.
        // Yes yes, Casey prefers nested loops and if statements over LINQ.
        bool defeatedAllElites = false;
        int totalElitesDefeated = 0;
        foreach (EncounterStats encounter in Progress.EncounterStats)
        {
            foreach (FightStats fightStat in encounter.FightStats)
            {
                if (fightStat.Character == character.Id && fightStat.Wins > 0)
                {
                    totalElitesDefeated++;
                }
            }
        }

        Log.Info($"Elites Defeated: {totalElitesDefeated}/{GetTotalEliteEncounterCount()}");
        if (totalElitesDefeated >= GetTotalEliteEncounterCount())
        {
            defeatedAllElites = true;
        }

        // If we defeated all unique elites, get the Epoch!
        if (defeatedAllElites && epoch != null)
        {
            TryObtainEpoch(epoch, localPlayer);
        }
    }

    /// <summary>
    /// Helper function to obtain character unlocks 1, 2, and 3.
    /// Requires the player to defeat a boss in an act for the first time (for each character!)
    /// </summary>
    private void ObtainCharUnlockEpoch(Player localPlayer, int act)
    {
        string idPrefix = localPlayer.Character.Id.Entry.ToUpper();
        EpochModel? epoch = null;

        switch (act)
        {
            case 0:
                epoch = EpochModel.Get($"{idPrefix}2_EPOCH");
                break;
            case 1:
                epoch = EpochModel.Get($"{idPrefix}3_EPOCH");
                break;
            case 2:
                epoch = EpochModel.Get($"{idPrefix}4_EPOCH");
                break;
            case 3:
                // This is Act 4. It's not yet implemented.
                Log.Error($"Act {act + 1} is not yet implemented.");
                // epoch = EpochModel.Get($"{idPrefix}8_EPOCH");
                break;
            default:
                Log.Error($"Unsupported Act: {act}");
                break;
        }

        if (epoch == null)
        {
            Log.Error("EpochModel was not found :(");
            return;
        }

        if (TryObtainEpoch(epoch, localPlayer))
        {
            Log.Info($"Epoch obtained for completing Act {act + 1}");
        }
    }

    /// <summary>
    /// If this Epoch's slot is available, we obtain it and return true.
    /// We return a bool as this determines if we display the ObtainEpochVfx.
    /// Returns true if you Obtained a new Epoch. False if you already have it OR the slot is unavailable.
    /// </summary>
    /// <param name="epochModel">Epoch to try to obtain.</param>
    /// <param name="localPlayer">
    /// Optional local player from when the epoch was obtained.
    /// Will be null when obtained outside of a climb (like on the main menu or after game over).
    /// </param>
    private bool TryObtainEpoch(EpochModel epochModel, Player? localPlayer)
    {
        if (IsEpochObtained(epochModel))
        {
            Log.Info($"Player already has Epoch: {epochModel.Id}");
            return false;
        }

        if (HasEpochSlot(epochModel))
        {
            Progress.Epochs.FirstOrDefault(epoch => epoch.Id == epochModel.Id)?.SetObtained(EpochState.Obtained);

            if (localPlayer != null)
            {
                localPlayer.DiscoveredEpochs++;
            }
        }
        else
        {
            Progress.Epochs.Add(new SerializableEpoch(epochModel.Id, EpochState.ObtainedNoSlot));

            if (localPlayer != null)
            {
                localPlayer.DiscoveredEpochs++;
            }
        }

        NGame.Instance?.AddChildSafely(NGainEpochVfx.Create(epochModel));
        return true;
    }

    /// <summary>
    /// Checks if this Epoch Slot exists. Ignores obtained/revealed/complete states.
    /// </summary>
    private bool HasEpochSlot(EpochModel epochModel)
    {
        return Progress.Epochs.Any(epoch => epoch.Id == epochModel.Id);
    }

    /// <summary>
    /// Helper function to see if an Epoch exists and whether it's been "Revealed", aka clicked on in the Timeline.
    /// </summary>
    public bool IsEpochRevealed<T>() where T : EpochModel
    {
        SerializableEpoch? epoch = Progress.Epochs.FirstOrDefault(e => e.Id == EpochModel.GetId<T>());
        return epoch is { State: EpochState.Revealed };
    }

    public bool HasEpoch(string id)
    {
        return Progress.Epochs.FirstOrDefault(epoch => epoch.Id == id)?.State >= EpochState.Revealed;
    }

    /// <summary>
    /// Checks if an Epoch is ObtainedNoSlot, Obtained, Complete, or Upgraded
    /// </summary>
    private bool IsEpochObtained(EpochModel epochModel)
    {
        SerializableEpoch? epoch = Progress.Epochs
            .FirstOrDefault(e => e.Id == epochModel.Id);

        if (epoch == null)
        {
            return false;
        }

        return epoch.State >= EpochState.ObtainedNoSlot;
    }

    /// <summary>
    /// Checks if an Epoch is ObtainedNoSlot, Obtained, Complete, or Upgraded
    /// </summary>
    private bool IsEpochObtained(string epochId)
    {
        SerializableEpoch? epoch = Progress.Epochs
            .FirstOrDefault(e => Equals(e.Id, epochId));

        if (epoch == null)
        {
            return false;
        }

        return epoch.State >= EpochState.ObtainedNoSlot;
    }

    #endregion

    /// <summary>
    /// Lots of checks and update of the progress save file after the end of each combat.
    /// </summary>
    public void UpdateAfterCombatWon(Player localPlayer, CombatRoom room)
    {
        CombatState combatState = room.CombatState;
        IClimbState climbState = combatState.ClimbState;
        CharacterModel character = localPlayer.Character;
        ModelId encounterId = combatState.Encounter!.Id;
        EncounterStats? encounterStat = Progress.EncounterStats.FirstOrDefault(stat => stat.Id == encounterId);

        // Log every encounter you've completed
        if (encounterStat == null)
        {
            Log.Info($"{character.Id} fought {encounterId} for the first time and WON >:)");
            EncounterStats newStat = new()
            {
                Id = encounterId,
                FightStats = []
            };

            FightStats fightEntry = new()
            {
                Character = character.Id,
                Wins = 1,
                Losses = 0,
                MaxAscension = climbState.AscensionLevel
            };

            newStat.FightStats.Add(fightEntry);

            Progress.EncounterStats.Add(newStat);
        }
        else
        {
            FightStats? fight = encounterStat.FightStats.FirstOrDefault(fight => fight.Character == character.Id);

            if (fight == null)
            {
                Log.Info($"While you have fought this encounter before, it's the first time {character.Id} is fighting it!");

                FightStats fightEntry = new()
                {
                    Character = character.Id,
                    Wins = 1,
                    Losses = 0,
                    MaxAscension = climbState.AscensionLevel
                };

                encounterStat.FightStats.Add(fightEntry);
            }
            else
            {
                encounterStat.IncrementWin(character.Id, climbState.AscensionLevel);
            }
        }

        if (room.RoomType == RoomType.Boss)
        {
            // Check for CharacterUnlock 1, 2, and 3.
            ObtainCharUnlockEpoch(localPlayer, climbState.CurrentActIndex);

            // Check for CharacterUnlock 5: Defeat 10 bosses with any character.
            CheckTenBossesDefeatedEpoch(localPlayer);

            // Check for Underdocks: Defeat all three Underdocks bosses.
            CheckUnderdocksEpoch(localPlayer);
        }
        else if (room.RoomType == RoomType.Elite)
        {
            // Check for CharacterUnlock 4
            CheckAllElitesDefeatedEpoch(localPlayer);
        }

        // Log every monster you've defeated (NOT encounter)
        foreach ((MonsterModel monster, _) in room.Encounter.MonstersWithSlots)
        {
            EnemyStats? enemyStat = Progress.EnemyStats.FirstOrDefault(enemy => enemy.Id == monster.Id);

            if (enemyStat == null)
            {
                Log.Info($"{character.Id} fought {monster.Id} for the first time and WON >:(");
                EnemyStats newStat = new()
                {
                    Id = monster.Id,
                    FightStats = []
                };

                FightStats fightEntry = new()
                {
                    Character = character.Id,
                    Wins = 1,
                    Losses = 0,
                    MaxAscension = climbState.AscensionLevel
                };

                newStat.FightStats.Add(fightEntry);
                Progress.EnemyStats.Add(newStat);

                // If we discover an Enemy, save it for the DISCOVERIES section in the game over screen
                localPlayer.DiscoveredEnemies++;
            }
            else
            {
                FightStats? fight = enemyStat.FightStats.FirstOrDefault(fight => fight.Character == character.Id);

                if (fight == null)
                {
                    Log.Info($"While you have fought this monster before, it's the first time {character.Id} is fighting it!");

                    FightStats fightEntry = new()
                    {
                        Character = character.Id,
                        Wins = 1,
                        Losses = 0,
                        MaxAscension = climbState.AscensionLevel
                    };

                    enemyStat.FightStats.Add(fightEntry);
                }
                else
                {
                    enemyStat.IncrementWin(character.Id, climbState.AscensionLevel);
                }
            }
        }
    }

    /// <summary>
    /// If the player wins a singleplayer climb, this logic checks if they're playing on Max Ascension.
    /// If so, increment it by 1.
    /// </summary>
    private static void IncrementSingleplayerAscension(SerializableClimb climb, CharacterStats charStats)
    {
        if (climb.Ascension == charStats.MaxAscension)
        {
            // Check if we're already on the highest allowed Ascension
            if (charStats.MaxAscension < AscensionManager.maxAscensionAllowed)
            {
                charStats.MaxAscension++;
                charStats.PreferredAscension = charStats.MaxAscension;
            }
        }
        else
        {
            Log.Info($"Not playing on max singleplayer ascension ({charStats.MaxAscension})");
        }
    }

    /// <summary>
    /// If the player wins a multiplayer climb, this logic checks if they're playing on Max Ascension.
    /// If so, increment it by 1.
    /// </summary>
    private void IncrementMultiplayerAscension(SerializableClimb climb)
    {
        if (climb.Ascension == Progress.MaxMultiplayerAscension)
        {
            // Check if we're already on the highest allowed Ascension
            if (Progress.MaxMultiplayerAscension < AscensionManager.maxAscensionAllowed)
            {
                Progress.MaxMultiplayerAscension++;
                Progress.PreferredMultiplayerAscension = Progress.MaxMultiplayerAscension;
            }
        }
        else
        {
            Log.Info($"Not playing on max multiplayer ascension ({Progress.MaxMultiplayerAscension})");
        }
    }

    /// <summary>
    /// Returns true if all ftues are disabled OR if the given ftue key exists (seen by the player before).
    /// Is also disabled if there's no game (test mode)
    /// </summary>
    /// <param name="ftueKey"></param>
    /// <returns></returns>
    public bool SeenFtue(string ftueKey)
    {
        if (!Progress.EnableFtues) return true;

        return Progress.FtueCompleted.Contains(ftueKey);
    }

    public void MarkFtueAsComplete(string ftueId)
    {
        if (Progress.FtueCompleted.Contains(ftueId)) return;

        Log.Info($"Player has seen ftue {ftueId}!");
        Progress.FtueCompleted.Add(ftueId);
        SaveProgress();
    }

    public void SetFtuesEnabled(bool enabled)
    {
        if (Progress.EnableFtues == enabled) return;

        Log.Info($"Player has set FTUEs enabled: {enabled}");
        Progress.EnableFtues = enabled;
        SaveProgress();
    }

    public void ResetFtues()
    {
        Log.Info($"Player has reset FTUEs to enabled");
        Progress.EnableFtues = true;
        Progress.FtueCompleted = [];
        SaveProgress();
    }

    /// <summary>
    /// Sets an Epoch to Obtained. If it doesn't exist, create a new Epoch and set it to ObtainedNoSlot.
    /// Called when players are obtaining Epochs during gameplay.
    /// </summary>
    public void ObtainEpoch(string epochId)
    {
        if (Progress.Epochs.Any(epoch => epoch.Id == epochId))
        {
            // Find and set the SerializableEpoch to isObtained = true
            Progress.Epochs.FirstOrDefault(epoch => epoch.Id == epochId)!.SetObtained(EpochState.Obtained);
        }
        else
        {
            Log.Info($"!!!!!!!!! {epochId} OBTAINED NO SLOT");
            // If the Epoch doesn't exist, create SerializableEpoch and set isObtained = true
            Progress.Epochs.Add(new SerializableEpoch(epochId, EpochState.ObtainedNoSlot));
        }
    }

    /// <summary>
    /// Sets or creates an Epoch to any EpochState we wish.
    /// Used by TimelineExpansions for overriding behaviors.
    /// </summary>
    public void ObtainEpochOverride(string epochId, EpochState state)
    {
        SerializableEpoch? epoch = Progress.Epochs.FirstOrDefault(epoch => epoch.Id == epochId);

        if (epoch != null)
        {
            Log.Info($"Set existing Epoch {epochId} to {state}");
            epoch.State = state;
        }
        else
        {
            Log.Info($"Create new Epoch {epochId} at {state}");
            Progress.Epochs.Add(new SerializableEpoch(epochId, state));
        }
    }

    // Cached after it's called once to save perf.
    private static int? _eliteEncounters;

    private static int GetTotalEliteEncounterCount()
    {
        return _eliteEncounters ??= ModelDb.AllEncounters
            .Where(e => e.RoomType == RoomType.Elite)
            .Select(e => e.GetType())
            .Distinct()
            .Count();
    }
}
