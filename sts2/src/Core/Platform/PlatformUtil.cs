using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Platform.Null;
using MegaCrit.Sts2.Core.Platform.Steam;

namespace MegaCrit.Sts2.Core.Platform;

public static class PlatformUtil
{
    private static readonly NullPlatformUtilStrategy _null = new();

#if !DISABLESTEAMWORKS
    private static readonly SteamPlatformUtilStrategy _steam = new();
#endif

    public static PlatformType PrimaryPlatform
    {
        get
        {
#if !DISABLESTEAMWORKS
            if (SteamInitializer.Initialized)
            {
                return PlatformType.Steam;
            }
#endif
            return PlatformType.None;
        }
    }

    private static IPlatformUtilStrategy GetPlatformUtil(PlatformType platformType)
    {
        return platformType switch
        {
            PlatformType.None => _null,
            PlatformType.Steam =>

            #if !DISABLESTEAMWORKS
                _steam,
            #else
                throw new InvalidOperationException("Steam is disabled!"),
            #endif

            _ => throw new ArgumentOutOfRangeException(nameof(platformType), platformType, null)
        };
    }

    public static string GetPlayerName(PlatformType platformType, ulong playerId)
    {
        return GetPlatformUtil(platformType).GetPlayerName(playerId);
    }

    public static ulong GetLocalPlayerId(PlatformType platformType)
    {
        return GetPlatformUtil(platformType).GetLocalPlayerId();
    }

    public static Task<IEnumerable<ulong>> GetFriendsWithOpenLobbies(PlatformType platformType)
    {
        return GetPlatformUtil(platformType).GetFriendsWithOpenLobbies();
    }

    public static void OpenUrl(string url)
    {
        GetPlatformUtil(PrimaryPlatform).OpenUrl(url);
    }
}
