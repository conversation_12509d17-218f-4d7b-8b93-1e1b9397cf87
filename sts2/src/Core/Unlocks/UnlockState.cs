using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Events;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Timeline;
using MegaCrit.Sts2.Core.Timeline.Epochs;

namespace MegaCrit.Sts2.Core.Unlocks;

/// <summary>
/// Represents the state of unlocked content for a given player.
/// The local player can encapsulate this state in their progress save. The real place this class is useful is in
/// multiplayer scenarios, where different players have different unlock states.
///
/// This is used in three places:
///  - In the Player class, to query a specific player's unlock state (e.g. for rewards or Attack potion generation)
///  - In the ClimbState class, to query the superset of all players' unlock states (e.g. for ancient generation)
///  - In the SaveManager class, to query the local player's unlock state outside of a climb (e.g. in the compendium)
///
/// For multiplayer, keep in mind which one is appropriate to use at a given time. Using the third when one of the first
/// two should be used will result in a state divergence.
///
/// In singleplayer, the first two are equivalent, but you should still consider when using the third is appropriate.
/// If you use the third during a run, epochs unlocked between a save and a load may be inappropriately included in the
/// climb.
/// </summary>
public class UnlockState
{
    public static readonly UnlockState none = new (new ProgressSave());
    public static readonly UnlockState all = new (
        EpochModel.AllIds(),
        ModelDb.AllEncounters.Select(e => e.Id),
        9999);

    /// <summary>
    /// The total set of epochs revealed.
    /// If this is the shared unlock set, this is the superset of all epochs from all players.
    /// </summary>
    private readonly HashSet<string> _unlockedEpochIds;

    /// <summary>
    /// The encounter IDs this player has seen (lost or won).
    /// If this is the shared unlock set, this is the superset of all encounters seen by all players.
    /// </summary>
    private readonly HashSet<ModelId> _encountersSeen;

    /// <summary>
    /// Ths number of climbs the player has done.
    /// If this is the shared unlock set, this is the **maximum** number of climbs among all players.
    /// </summary>
    public int NumberOfClimbs { get; }

    /// <summary>
    /// Returns the all the characters that the player has unlocked.
    /// </summary>
    public IEnumerable<CharacterModel> Characters
    {
        get
        {
            List<CharacterModel> unlockedCharacters = ModelDb.AllCharacters.ToList();

            if (!IsEpochRevealed<Silent1Epoch>())
            {
                unlockedCharacters.Remove(ModelDb.Character<Silent>());
            }

            if (!IsEpochRevealed<Regent1Epoch>())
            {
                unlockedCharacters.Remove(ModelDb.Character<Regent>());
            }

            if (!IsEpochRevealed<Necrobinder1Epoch>())
            {
                unlockedCharacters.Remove(ModelDb.Character<Necrobinder>());
            }

            if (!IsEpochRevealed<Defect1Epoch>())
            {
                unlockedCharacters.Remove(ModelDb.Character<Defect>());
            }

            return unlockedCharacters;
        }
    }

    /// <summary>
    /// Get all the ancients in the game that don't belong to a specific act and that the player has unlocked.
    /// </summary>
    public IEnumerable<AncientEventModel> SharedAncients
    {
        get
        {
            List<AncientEventModel> ancients = ModelDb.AllSharedAncients.ToList();

            if (!IsEpochRevealed<DarvEpoch>())
            {
                ancients.Remove(ModelDb.AncientEvent<Darv>());
            }

            return ancients;
        }
    }

    public bool HasSeenEncounter(EncounterModel encounter)
    {
        return _encountersSeen.Contains(encounter.Id);
    }

    /// <summary>
    /// Get all the card pools in the game that belong to specific characters and that the player has unlocked.
    /// </summary>
    public IEnumerable<CardPoolModel> CharacterCardPools => Characters.Select(c => c.CardPool);

    /// <summary>
    /// Get all the cards in the game that the player has unlocked.
    /// Be careful using this, it includes cards that you shouldn't be able to randomly roll for rewards.
    /// </summary>
    public IEnumerable<CardModel> Cards => CardPools
        .SelectMany(p => p.AllCards)
        .Concat(Characters.SelectMany(c => c.StartingDeck).Distinct())
        .Distinct();

    /// <summary>
    /// Get all the card pools in the game that the player has unlocked.
    /// </summary>
    public IEnumerable<CardPoolModel> CardPools => CharacterCardPools.Concat(ModelDb.AllSharedCardPools).Distinct();

    /// <summary>
    /// Injection constructor.
    /// </summary>
    public UnlockState(IEnumerable<string> unlockedEpochIds, IEnumerable<ModelId> encountersSeen, int numberOfClimbs)
    {
        _unlockedEpochIds = unlockedEpochIds.ToHashSet();
        _encountersSeen = encountersSeen.ToHashSet();
        NumberOfClimbs = numberOfClimbs;
    }

    /// <summary>
    /// Produces an unlock state from a progress save file. Only use this to derive an unlock state for the local player.
    /// </summary>
    /// <param name="progressSave">The progress save to derive the unlock state from.</param>
    public UnlockState(ProgressSave progressSave)
    {
        _unlockedEpochIds = progressSave.Epochs
            .Where(e => e.State == EpochState.Revealed)
            .Select(e => e.Id)
            .ToHashSet();

        _encountersSeen = progressSave.EncounterStats
            .Select(e => e.Id)
            // The progress save can contain IDs of deprecated encounters. In some error cases, it can also contain IDs
            // of non-encounters
            .Where(id => ModelDb.GetByIdOrNull<AbstractModel>(id) is EncounterModel)
            .ToHashSet();

        NumberOfClimbs = progressSave.NumberOfClimbs;
    }

    /// <summary>
    /// Produces an unlock state that is the union of all passed unlock states.
    /// In essence: In a multiplayer game, if one person has something unlocked, it will be unlocked for everyone in the
    /// multiplayer session.
    /// </summary>
    /// <param name="unlockStatesEnumerable">Set of states to merge together.</param>
    public UnlockState(IEnumerable<UnlockState> unlockStatesEnumerable)
    {
        UnlockState[] unlockStates = unlockStatesEnumerable.ToArray();
        _unlockedEpochIds = unlockStates.Select(s => s._unlockedEpochIds).SelectMany(m => m).Distinct().ToHashSet();
        _encountersSeen = unlockStates.Select(s => s._encountersSeen).SelectMany(b => b).Distinct().ToHashSet();
        NumberOfClimbs = unlockStates.Max(s => s.NumberOfClimbs);
    }

    /// <summary>
    /// Checks if this player has revealed an epoch on the timeline.
    /// </summary>
    /// <typeparam name="T">The type of epoch.</typeparam>
    public bool IsEpochRevealed<T>() where T: EpochModel
    {
        return _unlockedEpochIds.Contains(EpochModel.GetId<T>());
    }

    /// <summary>
    /// Marks an epoch as unlocked.
    /// This should be called on the SaveManager instance of this class when an epoch is unlocked in the Timeline. It
    /// should never be called in the Player or ClimbState instances.
    /// </summary>
    public void UnlockEpoch<T>() where T : EpochModel
    {
        UnlockEpoch(EpochModel.GetId<T>());
    }

    /// <summary>
    /// Marks an epoch as unlocked.
    /// This should be called on the SaveManager instance of this class when an epoch is unlocked in the Timeline. It
    /// should never be called in the Player or ClimbState instances.
    /// </summary>
    public void UnlockEpoch(string epochId)
    {
        _unlockedEpochIds.Add(epochId);
    }

    /// <returns>The amount of epochs the player has unlocked.</returns>
    public int EpochUnlockCount()
    {
        return _unlockedEpochIds.Count;
    }

    public SerializableUnlockState ToSerializable()
    {
        return new SerializableUnlockState
        {
            UnlockedEpochs = _unlockedEpochIds.ToList(),
            EncountersSeen = _encountersSeen.ToList(),
            NumberOfClimbs = NumberOfClimbs
        };
    }

    public static UnlockState FromSerializable(SerializableUnlockState unlockState)
    {
        // In old saves, unlock state can be null. It cannot ever be in newer saves, so I don't think it's worth making it nullable
        if (unlockState == null!) return all;
        return new UnlockState(unlockState.UnlockedEpochs, unlockState.EncountersSeen, unlockState.NumberOfClimbs);
    }
}
