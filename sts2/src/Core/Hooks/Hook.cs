using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands.Builders;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.CardRewardAlternatives;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Merchant;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.RestSite;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Hooks;

/// <summary>
/// A static class containing all of the gameplay hooks.
/// </summary>
public static class Hook
{
    #region Before/After Hooks

    /// <summary>
    /// See <see cref="AbstractModel.AfterActEntered"/>.
    /// </summary>
    public static async Task AfterActEntered(IClimbState climbState)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            await model.AfterActEntered();
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeAttack"/>.
    /// </summary>
    public static async Task BeforeAttack(CombatState combatState, AttackCommand command)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.BeforeAttack(command);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterAttack"/>.
    /// </summary>
    public static async Task AfterAttack(CombatState combatState, AttackCommand command)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterAttack(command);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterBlockBroken"/>.
    /// </summary>
    public static async Task AfterBlockBroken(CombatState combatState, Creature creature)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterBlockBroken(creature);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterBlockCleared"/>.
    /// </summary>
    public static async Task AfterBlockCleared(CombatState combatState, Creature creature)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterBlockCleared(creature);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeBlockGained"/>.
    /// </summary>
    public static async Task BeforeBlockGained(CombatState combatState, Creature creature, decimal amount, ValueProp props, CardModel? cardSource)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.BeforeBlockGained(creature, amount, props, cardSource);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterBlockGained"/>.
    /// </summary>
    public static async Task AfterBlockGained(CombatState combatState, Creature creature, decimal amount, ValueProp props, CardModel? cardSource)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterBlockGained(creature, amount, props, cardSource);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeCardAutoPlayed"/>.
    /// </summary>
    public static async Task BeforeCardAutoPlayed(CombatState combatState, CardModel card, Creature? target, AutoPlayType type)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.BeforeCardAutoPlayed(card, target, type);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterCardChangedPiles"/>.
    /// </summary>
    public static async Task AfterCardChangedPiles(IClimbState climbState, CombatState? combatState, CardModel card, PileType oldPile, AbstractModel? source)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            await model.AfterCardChangedPiles(card, oldPile, source);
            model.InvokeExecutionFinished();
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            await model.AfterCardChangedPilesLate(card, oldPile, source);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterCardDiscarded"/>.
    /// This takes a player choice context as an argument because it needs to block combat flow if a player choice is
    /// encountered.
    /// </summary>
    public static async Task AfterCardDiscarded(CombatState combatState, PlayerChoiceContext choiceContext, CardModel card)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            choiceContext.PushModel(model);
            await model.AfterCardDiscarded(choiceContext, card);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterCardDrawn"/>.
    /// This takes a player choice context as an argument because it needs to block combat flow if a player choice is
    /// encountered.
    /// </summary>
    public static async Task AfterCardDrawn(CombatState combatState, PlayerChoiceContext choiceContext, CardModel card, bool fromHandDraw)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            choiceContext.PushModel(model);
            await model.AfterCardDrawn(choiceContext, card, fromHandDraw);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterCardEnteredCombat"/>.
    /// </summary>
    public static async Task AfterCardEnteredCombat(CombatState combatState, CardModel card)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterCardEnteredCombat(card);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterCardExhausted"/>.
    /// This takes a player choice context as an argument because it needs to block combat flow if a player choice is
    /// encountered.
    /// </summary>
    public static async Task AfterCardExhausted(CombatState combatState, PlayerChoiceContext choiceContext, CardModel card, bool causedByEthereal)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            choiceContext.PushModel(model);
            await model.AfterCardExhausted(choiceContext, card, causedByEthereal);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterCardGeneratedForCombat"/>.
    /// </summary>
    public static async Task AfterCardGeneratedForCombat(CombatState combatState, CardModel card, bool addedByPlayer)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterCardGeneratedForCombat(card, addedByPlayer);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeCardPlayed"/>.
    /// </summary>
    public static async Task BeforeCardPlayed(CombatState combatState, CardModel card, Creature? target, int playCount, PileType resultPile)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.BeforeCardPlayed(card, target, playCount, resultPile);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterCardPlayed"/>.
    /// </summary>
    public static async Task AfterCardPlayed(CombatState combatState, PlayerChoiceContext choiceContext, CardModel card, Creature? target, int playCount)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            choiceContext.PushModel(model);
            await model.AfterCardPlayed(choiceContext, card, target, playCount);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            choiceContext.PushModel(model);
            await model.AfterCardPlayedLate(choiceContext, card, target, playCount);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeCardRemoved"/>.
    /// </summary>
    public static async Task BeforeCardRemoved(IClimbState climbState, CardModel card)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            await model.BeforeCardRemoved(card);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterCardRetained"/>.
    /// </summary>
    public static async Task AfterCardRetained(CombatState combatState, CardModel card)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterCardRetained(card);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeCombatStart"/>.
    /// </summary>
    public static async Task BeforeCombatStart(IClimbState climbState, CombatState? combatState)
    {
        // Note: This may sound like a combat-only hook, but combat start is relevant to non-combat models (like a deck
        // card that transforms when you start your third combat).
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            await model.BeforeCombatStart();
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterCombatEnd"/>.
    /// </summary>
    public static async Task AfterCombatEnd(IClimbState climbState, CombatState? combatState, CombatRoom room)
    {
        // Note: This may sound like a combat-only hook, but combat end is relevant to non-combat models (like Guilty).
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            await model.AfterCombatEnd(room);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterCombatVictory"/>.
    /// </summary>
    public static async Task AfterCombatVictory(IClimbState climbState, CombatState? combatState, CombatRoom room)
    {
        // Note: This may sound like a combat-only hook, but combat victory is relevant to non-combat models (like a
        // deck card that transforms after 3 combat victories).
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            await model.AfterCombatVictoryEarly(room);
            model.InvokeExecutionFinished();
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            await model.AfterCombatVictory(room);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterCreatureAddedToCombat"/>.
    /// </summary>
    public static async Task AfterCreatureAddedToCombat(CombatState combatState, Creature creature)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterCreatureAddedToCombat(creature);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterCurrentHpChanged"/>.
    /// </summary>
    public static async Task AfterCurrentHpChanged(IClimbState climbState, CombatState? combatState, Creature creature, decimal delta)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            await model.AfterCurrentHpChanged(creature, delta);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterDamageGiven"/>.
    /// </summary>
    public static async Task AfterDamageGiven(PlayerChoiceContext choiceContext, CombatState combatState, Creature? dealer, DamageResult results, ValueProp props, Creature target, CardModel? cardSource)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            choiceContext.PushModel(model);
            await model.AfterDamageGiven(choiceContext, dealer, results, props, target, cardSource);
            choiceContext.PopModel(model);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeDamageReceived"/>.
    /// </summary>
    public static async Task BeforeDamageReceived(PlayerChoiceContext choiceContext, IClimbState climbState, CombatState? combatState, Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            choiceContext.PushModel(model);
            await model.BeforeDamageReceived(choiceContext, target, amount, props, dealer, cardSource);
            choiceContext.PopModel(model);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterDamageReceived"/>.
    /// </summary>
    public static async Task AfterDamageReceived(PlayerChoiceContext choiceContext, IClimbState climbState, CombatState? combatState, Creature target, DamageResult result, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            choiceContext.PushModel(model);
            await model.AfterDamageReceived(choiceContext, target, result, props, dealer, cardSource);
            choiceContext.PopModel(model);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeDeath"/>.
    /// </summary>
    public static async Task BeforeDeath(IClimbState climbState, CombatState? combatState, Creature creature)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            await model.BeforeDeath(creature);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterDeath"/>.
    /// </summary>
    public static async Task AfterDeath(IClimbState climbState, CombatState? combatState, Creature creature, bool wasRemovalPrevented, float deathAnimLength)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, creature.CombatState!);
            Task task = model.AfterDeath(playerChoiceContext, creature, wasRemovalPrevented, deathAnimLength);
            await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterGoldGained"/>.
    /// </summary>
    public static async Task AfterGoldGained(IClimbState climbState, Player player)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            await model.AfterGoldGained(player);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterDiedToDoom"/>.
    /// </summary>
    public static async Task AfterDiedToDoom(CombatState combatState, Creature creature)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.AfterDiedToDoom(playerChoiceContext, creature);
            await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterEnergyReset"/>.
    /// </summary>
    public static async Task AfterEnergyReset(CombatState combatState, Player player)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterEnergyReset(player);
            model.InvokeExecutionFinished();
        }

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterEnergyResetLate(player);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterEnergySpent"/>.
    /// </summary>
    public static async Task AfterEnergySpent(CombatState combatState, CardModel card, int amount)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterEnergySpent(card, amount);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeFlush"/>.
    /// </summary>
    public static async Task BeforeFlush(CombatState combatState, Player player)
    {
        // Unlike other hooks, where we can run player choice during the turn while other things are executing, callers
        // of this hook expect the entirety of all hooks to be complete before this returns and end of turn occurs
        List<Task> tasksToAwait = [];

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, player.Creature.CombatState!);
            Task task = model.BeforeFlush(playerChoiceContext, player);
            bool ranToCompletion = await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);

            if (!ranToCompletion)
            {
                // Wait for the GameAction to complete, not just the underlying task. Otherwise, there is a chance that
                // checksums will be received out of order.
                tasksToAwait.Add(playerChoiceContext.GameAction!.CompletionTask);
            }
        }

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, player.Creature.CombatState!);
            Task task = model.BeforeFlushLate(playerChoiceContext, player);
            bool ranToCompletion = await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);

            if (!ranToCompletion)
            {
                // Wait for the GameAction to complete, not just the underlying task. Otherwise, there is a chance that
                // checksums will be received out of order.
                tasksToAwait.Add(playerChoiceContext.GameAction!.CompletionTask);
            }
        }

        // Note that if late hooks ever depend on the player choices of non-late hooks, then this should probably
        // also happen between the non-late and late executions
        await Task.WhenAll(tasksToAwait);
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterForge"/>.
    /// </summary>
    public static async Task AfterForge(CombatState combatState, decimal amount, Player forger)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterForge(amount, forger);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeHandDraw"/>.
    /// This takes a player choice context as an argument because it needs to block combat flow if a player choice is
    /// encountered.
    /// </summary>
    public static async Task BeforeHandDraw(CombatState combatState, Player player, PlayerChoiceContext playerChoiceContext)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            playerChoiceContext.PushModel(model);
            await model.BeforeHandDraw(player, playerChoiceContext, combatState);
            model.InvokeExecutionFinished();
            playerChoiceContext.PopModel(model);
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterHandEmptied"/>.
    /// This takes a player choice context as an argument because it needs to block combat flow if a player choice is
    /// encountered.
    /// </summary>
    public static async Task AfterHandEmptied(CombatState combatState, PlayerChoiceContext choiceContext, Player player)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            choiceContext.PushModel(model);
            await model.AfterHandEmptied(choiceContext, player);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterItemPurchased"/>.
    /// </summary>
    public static async Task AfterItemPurchased(IClimbState climbState, Player player, MerchantEntry itemPurchased, int goldSpent)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            await model.AfterItemPurchased(player, itemPurchased, goldSpent);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterMapGenerated"/>.
    /// </summary>
    public static async Task AfterMapGenerated(IClimbState climbState, ActMap map, int actIndex)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            await model.AfterMapGenerated(map, actIndex);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterModifyingBlockAmount"/>.
    /// </summary>
    public static async Task AfterModifyingBlockAmount(CombatState combatState, decimal modifiedBlock, CardModel? cardSource, IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in combatState.IterateHookListeners())
        {
            if (!modifiers.Contains(modifier)) continue;

            await modifier.AfterModifyingBlockAmount(modifiedBlock, cardSource);
            modifier.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterModifyingCardPlayCount"/>.
    /// </summary>
    public static async Task AfterModifyingCardPlayCount(CombatState combatState, CardModel card, IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in combatState.IterateHookListeners())
        {
            if (!modifiers.Contains(modifier)) continue;

            await modifier.AfterModifyingCardPlayCount(card);
            modifier.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterModifyingCardRewardOptions"/>.
    /// </summary>
    public static async Task AfterModifyingCardRewardOptions(IClimbState climbState, IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in climbState.IterateHookListeners(null))
        {
            if (!modifiers.Contains(modifier)) continue;

            await modifier.AfterModifyingCardRewardOptions();
            modifier.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterModifyingDamageAmount"/>.
    /// </summary>
    public static async Task AfterModifyingDamageAmount(IClimbState climbState, CombatState? combatState, CardModel? cardSource, IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in climbState.IterateHookListeners(combatState))
        {
            if (!modifiers.Contains(modifier)) continue;

            await modifier.AfterModifyingDamageAmount(cardSource);
            modifier.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterModifyingHandDraw"/>.
    /// </summary>
    public static async Task AfterModifyingHandDraw(CombatState combatState, IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in combatState.IterateHookListeners())
        {
            if (!modifiers.Contains(modifier)) continue;

            await modifier.AfterModifyingHandDraw();
            modifier.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterModifyingHpLostBeforeOsty"/>.
    /// </summary>
    public static async Task AfterModifyingHpLostBeforeOsty(IClimbState climbState, CombatState? combatState, IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in climbState.IterateHookListeners(combatState))
        {
            if (!modifiers.Contains(modifier)) continue;

            await modifier.AfterModifyingHpLostBeforeOsty();
            modifier.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterModifyingHpLostAfterOsty"/>.
    /// </summary>
    public static async Task AfterModifyingHpLostAfterOsty(IClimbState climbState, CombatState? combatState, IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in climbState.IterateHookListeners(combatState))
        {
            if (!modifiers.Contains(modifier)) continue;

            await modifier.AfterModifyingHpLostAfterOsty();
            modifier.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterModifyingOrbPassiveTriggerCount"/>.
    /// </summary>
    public static async Task AfterModifyingOrbPassiveTriggerCount(CombatState combatState, OrbModel orb, IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in combatState.IterateHookListeners())
        {
            if (!modifiers.Contains(modifier)) continue;

            await modifier.AfterModifyingOrbPassiveTriggerCount(orb);
            modifier.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterModifyingPowerAmountGiven"/>.
    /// </summary>
    public static async Task AfterModifyingPowerAmountGiven(CombatState combatState, IEnumerable<AbstractModel> modifiers, PowerModel modifiedPower)
    {
        foreach (AbstractModel modifier in combatState.IterateHookListeners())
        {
            if (!modifiers.Contains(modifier)) continue;

            await modifier.AfterModifyingPowerAmountGiven(modifiedPower);
            modifier.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterModifyingPowerAmountReceived"/>.
    /// </summary>
    public static async Task AfterModifyingPowerAmountReceived(CombatState combatState, IEnumerable<AbstractModel> modifiers, PowerModel modifiedPower)
    {
        foreach (AbstractModel modifier in combatState.IterateHookListeners())
        {
            if (!modifiers.Contains(modifier)) continue;

            await modifier.AfterModifyingPowerAmountReceived(modifiedPower);
            modifier.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterModifyingRewards"/>.
    /// </summary>
    public static async Task AfterModifyingRewards(IClimbState climbState, IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in climbState.IterateHookListeners(null))
        {
            if (!modifiers.Contains(modifier)) continue;

            await modifier.AfterModifyingRewards();
            modifier.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterOrbChanneled"/>.
    /// </summary>
    public static async Task AfterOrbChanneled(CombatState combatState, PlayerChoiceContext choiceContext, Player player, OrbModel orb)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            choiceContext.PushModel(model);
            await model.AfterOrbChanneled(choiceContext, player, orb);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterOrbEvoked"/>.
    /// </summary>
    public static async Task AfterOrbEvoked(PlayerChoiceContext choiceContext, CombatState combatState, OrbModel orb, IEnumerable<Creature> targets)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterOrbEvoked(choiceContext, orb, targets);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterOstyRevived"/>.
    /// </summary>
    public static async Task AfterOstyRevived(CombatState combatState, Creature osty)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterOstyRevived(osty);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterPlayerTurnStart"/>.
    /// </summary>
    public static async Task AfterPlayerTurnStart(CombatState combatState, PlayerChoiceContext choiceContext, Player player)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            choiceContext.PushModel(model);
            await model.AfterPlayerTurnStart(choiceContext, player);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterPotionDiscarded"/>.
    /// </summary>
    public static async Task AfterPotionDiscarded(IClimbState climbState, CombatState? combatState, PotionModel potion)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            await model.AfterPotionDiscarded(potion);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterPotionProcured"/>.
    /// </summary>
    public static async Task AfterPotionProcured(IClimbState climbState, CombatState? combatState, PotionModel potion)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            await model.AfterPotionProcured(potion);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforePotionUsed"/>.
    /// </summary>
    public static async Task BeforePotionUsed(IClimbState climbState, CombatState? combatState, PotionModel potion, Creature? target)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            await model.BeforePotionUsed(potion, target);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterPotionUsed"/>.
    /// </summary>
    public static async Task AfterPotionUsed(IClimbState climbState, CombatState? combatState, PotionModel potion, Creature? target)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            await model.AfterPotionUsed(potion, target);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforePowerAmountChanged"/>.
    /// </summary>
    public static async Task BeforePowerAmountChanged(CombatState combatState, PowerModel power, decimal amount, Creature target, Creature? applier, CardModel? cardSource)
    {
        foreach (AbstractModel modifier in combatState.IterateHookListeners())
        {
            await modifier.BeforePowerAmountChanged(power, amount, target, applier, cardSource);
            modifier.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterPowerAmountChanged"/>.
    /// </summary>
    public static async Task AfterPowerAmountChanged(CombatState combatState, PowerModel power, decimal amount, Creature? applier)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterPowerAmountChanged(power, amount, applier);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterPreventingBlockClear"/>.
    /// </summary>
    public static async Task AfterPreventingBlockClear(CombatState combatState, AbstractModel preventer, Creature creature)
    {
        if (!combatState.IterateHookListeners().Contains(preventer)) return;

        await preventer.AfterPreventingBlockClear(preventer, creature);
        preventer.InvokeExecutionFinished();
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterPreventingDeath"/>.
    /// </summary>
    public static async Task AfterPreventingDeath(CombatState combatState, AbstractModel preventer, Creature creature)
    {
        if (!combatState.IterateHookListeners().Contains(preventer)) return;

        await preventer.AfterPreventingDeath(creature);
        preventer.InvokeExecutionFinished();
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterPreventingDraw"/>.
    /// </summary>
    public static async Task AfterPreventingDraw(CombatState combatState, AbstractModel modifier)
    {
        if (!combatState.IterateHookListeners().Contains(modifier)) return;

        await modifier.AfterPreventingDraw();
        modifier.InvokeExecutionFinished();
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterRestSiteHeal"/>.
    /// </summary>
    public static async Task AfterRestSiteHeal(IClimbState climbState, Player player)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            await model.AfterRestSiteHeal(player);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterRestSiteSmith"/>.
    /// </summary>
    public static async Task AfterRestSiteSmith(IClimbState climbState, Player player)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            await model.AfterRestSiteSmith(player);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeRewardsOffered"/>.
    /// </summary>
    public static async Task BeforeRewardsOffered(IClimbState climbState, Player player, IReadOnlyList<Reward> rewards)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            await model.BeforeRewardsOffered(player, rewards);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterRewardTaken"/>.
    /// </summary>
    public static async Task AfterRewardTaken(IClimbState climbState, Player player, Reward reward)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            await model.AfterRewardTaken(player, reward);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeRoomEntered"/>.
    /// </summary>
    public static async Task BeforeRoomEntered(IClimbState climbState, AbstractRoom room)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            await model.BeforeRoomEntered(room);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterRoomEntered"/>.
    /// </summary>
    public static async Task AfterRoomEntered(IClimbState climbState, AbstractRoom room)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            await model.AfterRoomEntered(room);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterShuffle"/>.
    /// This takes a player choice context as an argument because it needs to block combat flow if a player choice is
    /// encountered.
    /// </summary>
    public static async Task AfterShuffle(CombatState combatState, PlayerChoiceContext choiceContext, Player shuffler)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            choiceContext.PushModel(model);
            await model.AfterShuffle(choiceContext, shuffler);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeSideTurnStart"/>.
    /// </summary>
    public static async Task BeforeSideTurnStart(CombatState combatState, CombatSide side)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.BeforeSideTurnStart(playerChoiceContext, side, combatState);
            await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterSideTurnStart"/>.
    /// </summary>
    public static async Task AfterSideTurnStart(CombatState combatState, CombatSide side)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.AfterSideTurnStart(playerChoiceContext, side, combatState);
            await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterStarsGained"/>.
    /// </summary>
    public static async Task AfterStarsGained(CombatState combatState, int amount, Player gainer)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterStarsGained(amount, gainer);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterStarsSpent"/>.
    /// </summary>
    public static async Task AfterStarsSpent(CombatState combatState, int amount, Player spender)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterStarsSpent(amount, spender);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterSummon"/>.
    /// This takes a player choice context as an argument because it needs to block combat flow if a player choice is
    /// encountered.
    /// </summary>
    public static async Task AfterSummon(CombatState combatState, PlayerChoiceContext choiceContext, Player summoner, decimal amount)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            choiceContext.PushModel(model);
            await model.AfterSummon(choiceContext, summoner, amount);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterTakingExtraTurn"/>.
    /// </summary>
    public static async Task AfterTakingExtraTurn(CombatState combatState, Player player)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            await model.AfterTakingExtraTurn(player);
            model.InvokeExecutionFinished();
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.BeforeTurnEnd"/>.
    /// </summary>
    public static async Task BeforeTurnEnd(CombatState combatState, CombatSide side)
    {
        // Unlike other hooks, where we can run player choice during the turn while other things are executing, callers
        // of this hook expect the entirety of all hooks to be complete before this returns and end of turn occurs
        List<Task> tasksToAwait = [];

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.BeforeTurnEndVeryEarly(playerChoiceContext, side);
            bool ranToCompletion = await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);

            if (!ranToCompletion)
            {
                // Wait for the GameAction to complete, not just the underlying task. Otherwise, there is a chance that
                // checksums will be received out of order.
                tasksToAwait.Add(playerChoiceContext.GameAction!.CompletionTask);
            }
        }

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.BeforeTurnEndEarly(playerChoiceContext, side);
            bool ranToCompletion = await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);

            if (!ranToCompletion)
            {
                // Wait for the GameAction to complete, not just the underlying task. Otherwise, there is a chance that
                // checksums will be received out of order.
                tasksToAwait.Add(playerChoiceContext.GameAction!.CompletionTask);
            }
        }

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.BeforeTurnEnd(playerChoiceContext, side);
            bool ranToCompletion = await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);

            if (!ranToCompletion)
            {
                // Wait for the GameAction to complete, not just the underlying task. Otherwise, there is a chance that
                // checksums will be received out of order.
                tasksToAwait.Add(playerChoiceContext.GameAction!.CompletionTask);
            }
        }

        // Note that if late hooks ever depend on the player choices of non-late hooks, then this should probably
        // also happen between the non-late and late executions
        await Task.WhenAll(tasksToAwait);
    }

    /// <summary>
    /// See <see cref="AbstractModel.AfterTurnEnd"/>.
    /// </summary>
    public static async Task AfterTurnEnd(CombatState combatState, CombatSide side)
    {
        // Unlike other hooks, where we can run player choice during the turn while other things are executing, callers
        // of this hook expect the entirety of all hooks to be complete before this returns and end of turn occurs
        List<Task> tasksToAwait = [];

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.AfterTurnEnd(playerChoiceContext, side);
            bool ranToCompletion = await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);

            if (!ranToCompletion)
            {
                // Wait for the GameAction to complete, not just the underlying task. Otherwise, there is a chance that
                // checksums will be received out of order.
                tasksToAwait.Add(playerChoiceContext.GameAction!.CompletionTask);
            }
        }

        await Task.WhenAll(tasksToAwait);
    }

    #endregion

    #region Modify Hooks

    /// <summary>
    /// See <see cref="AbstractModel.ModifyAttackHitCount"/>.
    /// </summary>
    public static decimal ModifyAttackHitCount(CombatState combatState, AttackCommand attackCommand, int originalHitCount)
    {
        int finalHitCount = originalHitCount;

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            finalHitCount = model.ModifyAttackHitCount(attackCommand, finalHitCount);
        }

        return finalHitCount;
    }

    /// <summary>
    /// See <see cref="AbstractModel.TryModifyBlockReceived"/>.
    /// </summary>
    public static decimal ModifyBlockReceived(CombatState combatState, Creature target, decimal block, ValueProp props, CardModel? cardSource, out IEnumerable<AbstractModel> modifiers)
    {
        List<AbstractModel> modifyingModels = [];
        decimal finalBlock = block;

        if (cardSource is { Enchantment: not null })
        {
            finalBlock = cardSource.Enchantment.EnchantBlock(finalBlock, props);
        }

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            bool modified = model.TryModifyBlockReceived(target, finalBlock, props, cardSource, out finalBlock);

            if (modified)
            {
                modifyingModels.Add(model);
            }
        }

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            bool modified = model.TryModifyBlockReceivedLate(target, finalBlock, props, cardSource, out finalBlock);

            if (modified)
            {
                modifyingModels.Add(model);
            }
        }

        modifiers = modifyingModels;

        return Math.Max(0, finalBlock);
    }

    /// <summary>
    /// See <see cref="AbstractModel.TryModifyCardBeingAddedToDeck"/>.
    /// </summary>
    public static CardModel ModifyCardBeingAddedToDeck(IClimbState climbState, CardModel card, out List<AbstractModel> modifyingModels)
    {
        modifyingModels = [];

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            bool modified = model.TryModifyCardBeingAddedToDeck(card, out CardModel? newCard);

            if (modified && newCard != null)
            {
                modifyingModels.Add(model);
                card = newCard;
            }

            model.InvokeExecutionFinished();
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            bool modified = model.TryModifyCardBeingAddedToDeckLate(card, out CardModel? newCard);

            if (modified && newCard != null)
            {
                modifyingModels.Add(model);
                card = newCard;
            }

            model.InvokeExecutionFinished();
        }

        return card;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyCardPlayCount"/>.
    /// </summary>
    public static int ModifyCardPlayCount(CombatState combatState, CardModel card, int playCount, Creature? target, out List<AbstractModel> modifyingModels)
    {
        modifyingModels = [];
        int finalPlayCount = playCount;

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            int prevPlayCount = finalPlayCount;
            finalPlayCount = model.ModifyCardPlayCount(card, target, finalPlayCount);

            if (finalPlayCount != prevPlayCount)
            {
                modifyingModels.Add(model);
            }
        }

        return finalPlayCount;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyCardPlayResultPileTypeAndPosition"/>.
    /// </summary>
    public static (PileType, CardPilePosition) ModifyCardPlayResultPileTypeAndPosition(CombatState combatState, CardModel card, PileType pileType, CardPilePosition position, out IEnumerable<AbstractModel> modifiers)
    {
        PileType finalPileType = pileType;
        CardPilePosition finalPosition = position;
        List<AbstractModel> modifyingModels = [];

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            PileType prevPileType = finalPileType;
            CardPilePosition prevPosition = finalPosition;
            (finalPileType, finalPosition) = model.ModifyCardPlayResultPileTypeAndPosition(card, finalPileType, finalPosition);

            if (prevPileType != finalPileType || prevPosition != finalPosition)
            {
                modifyingModels.Add(model);
            }
        }

        modifiers = modifyingModels;
        return (finalPileType, finalPosition);
    }

    /// <summary>
    /// See <see cref="AbstractModel.TryModifyCardRewardAlternatives"/>.
    /// </summary>
    public static IEnumerable<AbstractModel> ModifyCardRewardAlternatives(IClimbState climbState, Player player, CardReward cardReward, List<CardRewardAlternative> alternatives)
    {
        List<AbstractModel> modifiers = [];

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            if (model.TryModifyCardRewardAlternatives(player, cardReward, alternatives))
            {
                modifiers.Add(model);
            }
        }

        return modifiers;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyCardRewardCreationOptions"/>.
    /// </summary>
    public static CardCreationOptions ModifyCardRewardCreationOptions(IClimbState climbState, Player player, CardCreationOptions options)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            options = model.ModifyCardRewardCreationOptions(player, options);
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            options = model.ModifyCardRewardCreationOptionsLate(player, options);
        }

        return options;
    }

    /// <summary>
    /// See <see cref="AbstractModel.TryModifyCardRewardOptions"/>.
    /// </summary>
    public static bool TryModifyCardRewardOptions(IClimbState climbState, Player player, List<CardCreationResult> cardRewardOptions, CardCreationOptions creationOptions, out List<AbstractModel> modifiers)
    {
        bool modified = false;
        modifiers = [];

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            bool didModify = model.TryModifyCardRewardOptions(player, cardRewardOptions, creationOptions);
            modified = modified || didModify;
            modifiers.Add(model);
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            bool didModify = model.TryModifyCardRewardOptionsLate(player, cardRewardOptions, creationOptions);
            modified = modified || didModify;
            modifiers.Add(model);
        }

        return modified;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyCardRewardUpgradeOdds"/>.
    /// </summary>
    public static decimal ModifyCardRewardUpgradeOdds(IClimbState climbState, Player player, CardModel card, decimal originalOdds)
    {
        decimal finalOdds = originalOdds;

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            finalOdds = model.ModifyCardRewardUpgradeOdds(player, card, finalOdds);
        }

        return finalOdds;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyDamageGiven"/> and <see cref="AbstractModel.ModifyDamageReceived"/>.
    /// </summary>
    public static decimal ModifyDamageAmount(IClimbState climbState, CombatState? combatState, Creature? receiver, Creature? dealer, decimal damage, ValueProp props, CardModel? cardSource, CardPreviewMode previewMode, out IEnumerable<AbstractModel> modifiers)
    {
        List<AbstractModel> modifyingModels = [];

        decimal modifiedDamage = damage;

        if (cardSource is { Enchantment: not null })
        {
            modifiedDamage = cardSource.Enchantment.EnchantDamage(modifiedDamage, props);
        }

        modifiedDamage = ModifyDamageGiven(climbState, combatState, dealer, modifiedDamage, props, receiver, cardSource, modifyingModels);

        if (receiver == null)
        {
            // If we're doing a damage preview with an AOE/randomly-targeted card, run the ModifyDamageReceived hooks
            // for powers that every enemy has.
            // For example, if all enemies have Vulnerable, Breakthrough will preview it.
            bool shouldPreviewSharedPowers =
                previewMode == CardPreviewMode.MultiCreatureTargeting &&
                // Note: We have to separate the owner check from the other checks so it short-circuits properly.
                cardSource is
                {
                    TargetType: TargetType.AllEnemies or TargetType.RandomEnemy,
                    Pile.Type: PileType.Hand or PileType.Play
                };

            if (shouldPreviewSharedPowers)
            {
                Creature owner = cardSource!.Owner.Creature;
                List<Creature> enemies = owner.CombatState?.GetOpponentsOf(owner).Where(c => c.IsHittable).ToList() ?? [];
                Creature? firstEnemy = enemies.FirstOrDefault();
                List<PowerModel> sharedPowers = [];

                if (firstEnemy != null)
                {
                    // Build up a list of powers that every enemy has.
                    foreach (PowerModel power in firstEnemy.Powers)
                    {
                        // Only preview powers that explicitly should be previewed when every enemy has them.
                        if (!power.ShouldPreviewWhenSharedByAllEnemies) continue;

                        // Only preview powers that every enemy has.
                        if (!enemies.All(e => e.HasPower(power.Id))) continue;

                        sharedPowers.Add(power);
                    }

                    foreach (PowerModel power in sharedPowers)
                    {
                        modifiedDamage = power.ModifyDamageReceivedEarly(firstEnemy, modifiedDamage, props, dealer, cardSource);
                    }

                    foreach (PowerModel power in sharedPowers)
                    {
                        modifiedDamage = power.ModifyDamageReceived(firstEnemy, modifiedDamage, props, dealer, cardSource);
                    }

                    foreach (PowerModel power in sharedPowers)
                    {
                        modifiedDamage = power.ModifyDamageReceivedLate(firstEnemy, modifiedDamage, props, dealer, cardSource);
                    }
                }
            }
        }
        else
        {
            modifiedDamage = ModifyDamageReceived(climbState, combatState, receiver, modifiedDamage, props, dealer, cardSource, modifyingModels);
        }

        modifiers = modifyingModels;

        return Math.Max(0, modifiedDamage);
    }

    /// <summary>
    /// See <see cref="AbstractModel.TryModifyEnergyCostInCombat"/>.
    /// </summary>
    public static decimal ModifyEnergyCostInCombat(CombatState combatState, CardModel card, decimal originalCost)
    {
        return ModifyEnergyCostInCombat(card, originalCost, combatState.IterateHookListeners());
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyExtraRestSiteHealText"/>.
    /// </summary>
    public static IReadOnlyList<LocString> ModifyExtraRestSiteHealText(IClimbState climbState, IReadOnlyList<LocString> extraText)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            extraText = model.ModifyExtraRestSiteHealText(extraText);
        }

        return extraText;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyGeneratedMap"/>.
    /// </summary>
    public static ActMap ModifyGeneratedMap(IClimbState climbState, ActMap map, int actIndex)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            map = model.ModifyGeneratedMap(climbState, map, actIndex);
            model.InvokeExecutionFinished();
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            map = model.ModifyGeneratedMapLate(climbState, map, actIndex);
            model.InvokeExecutionFinished();
        }

        return map;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyHandDraw"/>.
    /// </summary>
    public static decimal ModifyHandDraw(CombatState combatState, Player player, decimal originalCardCount, out IEnumerable<AbstractModel> modifiers)
    {
        decimal finalCardCount = originalCardCount;
        List<AbstractModel> modifyingModels = [];

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            decimal prevCount = finalCardCount;
            finalCardCount = model.ModifyHandDraw(player, finalCardCount);

            if ((int)prevCount != (int)finalCardCount)
            {
                modifyingModels.Add(model);
            }
        }

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            decimal prevCount = finalCardCount;
            finalCardCount = model.ModifyHandDrawLate(player, finalCardCount);

            if ((int)prevCount != (int)finalCardCount)
            {
                modifyingModels.Add(model);
            }
        }

        modifiers = modifyingModels;
        return finalCardCount;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyHealAmount"/>.
    /// </summary>
    public static decimal ModifyHealAmount(IClimbState climbState, CombatState? combatState, Creature creature, decimal amount)
    {
        decimal finalAmount = amount;

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            finalAmount = model.ModifyHealAmount(creature, finalAmount);
        }

        return finalAmount;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyHpLostBeforeOsty"/>.
    /// </summary>
    public static decimal ModifyHpLostBeforeOsty(IClimbState climbState, CombatState? combatState, Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource, out IEnumerable<AbstractModel> modifiers)
    {
        decimal finalAmount = amount;
        List<AbstractModel> modifyingModels = [];

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyHpLostBeforeOsty(target, finalAmount, props, dealer, cardSource);

            if ((int)prevAmount != (int)finalAmount)
            {
                modifyingModels.Add(model);
            }
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyHpLostBeforeOstyLate(target, finalAmount, props, dealer, cardSource);

            if ((int)prevAmount != (int)finalAmount)
            {
                modifyingModels.Add(model);
            }
        }

        modifiers = modifyingModels;
        return finalAmount;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyHpLostBeforeOsty"/>.
    /// </summary>
    public static decimal ModifyHpLostAfterOsty(IClimbState climbState, CombatState? combatState, Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource, out IEnumerable<AbstractModel> modifiers)
    {
        decimal finalAmount = amount;
        List<AbstractModel> modifyingModels = [];

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyHpLostAfterOsty(target, finalAmount, props, dealer, cardSource);

            if ((int)prevAmount != (int)finalAmount)
            {
                modifyingModels.Add(model);
            }
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyHpLostAfterOstyLate(target, finalAmount, props, dealer, cardSource);

            if ((int)prevAmount != (int)finalAmount)
            {
                modifyingModels.Add(model);
            }
        }

        modifiers = modifyingModels;
        return finalAmount;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyMaxEnergy"/>.
    /// </summary>
    public static decimal ModifyMaxEnergy(CombatState combatState, Player player, decimal amount)
    {
        decimal finalAmount = amount;

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            finalAmount = model.ModifyMaxEnergy(player, finalAmount);
        }

        return finalAmount;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyMerchantCardCreationResults"/>.
    /// </summary>
    public static void ModifyMerchantCardCreationResults(IClimbState climbState, Player player, List<CardCreationResult> cards)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            model.ModifyMerchantCardCreationResults(player, cards);
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyMerchantCardPool"/>.
    /// </summary>
    public static IEnumerable<CardModel> ModifyMerchantCardPool(IClimbState climbState, Player player, IEnumerable<CardModel> options)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            options = model.ModifyMerchantCardPool(player, options);
        }

        return options;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyMerchantCardPool"/>.
    /// </summary>
    public static CardRarity ModifyMerchantCardRarity(IClimbState climbState, Player player, CardRarity rarity)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            rarity = model.ModifyMerchantCardRarity(player, rarity);
        }

        return rarity;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyMerchantPrice"/>.
    /// </summary>
    public static decimal ModifyMerchantPrice(IClimbState climbState, Player player, MerchantEntry entry, decimal result)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            result = model.ModifyMerchantPrice(player, entry, result);
        }

        return result;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyNextEvent"/>.
    /// </summary>
    public static EventModel ModifyNextEvent(IClimbState climbState, EventModel currentEvent)
    {
        EventModel modifiedEvent = currentEvent;

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            modifiedEvent = model.ModifyNextEvent(modifiedEvent);
        }

        return modifiedEvent;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyOddsIncreaseForUnrolledRoomType"/>.
    /// </summary>
    public static float ModifyOddsIncreaseForUnrolledRoomType(IClimbState climbState, RoomType roomType, float oddsIncrease)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            oddsIncrease = model.ModifyOddsIncreaseForUnrolledRoomType(roomType, oddsIncrease);
        }

        return oddsIncrease;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyOrbPassiveTriggerCounts"/>.
    /// </summary>
    public static int ModifyOrbPassiveTriggerCount(CombatState combatState, OrbModel orb, int triggerCount, out List<AbstractModel> modifyingModels)
    {
        modifyingModels = [];
        int finalTriggerCount = triggerCount;

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            int prevTriggerCount = finalTriggerCount;
            finalTriggerCount = model.ModifyOrbPassiveTriggerCounts(orb, finalTriggerCount);

            if (finalTriggerCount != prevTriggerCount)
            {
                modifyingModels.Add(model);
            }
        }

        return finalTriggerCount;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyOrbValue"/>.
    /// </summary>
    public static decimal ModifyOrbValue(CombatState combatState, Player player, decimal amount)
    {
        decimal finalAmount = amount;

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            finalAmount = model.ModifyOrbValue(player, finalAmount);
        }

        return finalAmount;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyPowerAmountGiven"/>.
    /// </summary>
    public static decimal ModifyPowerAmountGiven(CombatState combatState, PowerModel power, Creature giver, decimal amount, Creature? target, CardModel? cardSource, out IEnumerable<AbstractModel> modifiers)
    {
        decimal finalAmount = amount;
        List<AbstractModel> modifyingModels = [];

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            decimal newAmount = model.ModifyPowerAmountGiven(power, giver, finalAmount, target, cardSource);

            if ((int)newAmount != (int)finalAmount)
            {
                finalAmount = newAmount;
                modifyingModels.Add(model);
            }
        }

        modifiers = modifyingModels;
        return finalAmount;
    }

    /// <summary>
    /// See <see cref="AbstractModel.TryModifyPowerAmountReceived"/>.
    /// </summary>
    public static decimal ModifyPowerAmountReceived(CombatState combatState, PowerModel canonicalPower, Creature target, decimal amount, Creature? giver, out IEnumerable<AbstractModel> modifiers)
    {
        decimal finalAmount = amount;
        List<AbstractModel> modifyingModels = [];

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (model.TryModifyPowerAmountReceived(canonicalPower, target, finalAmount, giver, out decimal modifiedAmount))
            {
                finalAmount = modifiedAmount;
                modifyingModels.Add(model);
            }
        }

        modifiers = modifyingModels;
        return finalAmount;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyRestSiteHealAmount"/>.
    /// </summary>
    public static decimal ModifyRestSiteHealAmount(IClimbState climbState, Creature creature, decimal amount)
    {
        decimal finalAmount = amount;

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            finalAmount = model.ModifyRestSiteHealAmount(creature, finalAmount);
        }

        return ModifyHealAmount(climbState, null, creature, finalAmount);
    }

    /// <summary>
    /// See <see cref="AbstractModel.TryModifyRestSiteOptions"/>.
    /// </summary>
    public static IEnumerable<AbstractModel> ModifyRestSiteOptions(IClimbState climbState, Player player, ICollection<RestSiteOption> options)
    {
        List<AbstractModel> modifiers = [];

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            if (model.TryModifyRestSiteOptions(player, options))
            {
                modifiers.Add(model);
            }
        }

        return modifiers;
    }

    /// <summary>
    /// See <see cref="AbstractModel.TryModifyRestSiteHealRewards"/>.
    /// </summary>
    public static IEnumerable<AbstractModel> ModifyRestSiteHealRewards(IClimbState climbState, Player player, List<Reward> rewards)
    {
        List<AbstractModel> modifiers = [];
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            if (model.TryModifyRestSiteHealRewards(player, rewards))
            {
                modifiers.Add(model);
            }
        }

        return modifiers;
    }

    /// <summary>
    /// See <see cref="AbstractModel.TryModifyRewards"/>.
    /// </summary>
    public static IEnumerable<AbstractModel> ModifyRewards(IClimbState climbState, Player player, List<Reward> rewards, AbstractRoom? room)
    {
        List<AbstractModel> modifiers = [];

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            if (model.TryModifyRewards(player, rewards, room))
            {
                modifiers.Add(model);
            }
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            if (model.TryModifyRewardsLate(player, rewards, room))
            {
                modifiers.Add(model);
            }
        }

        return modifiers;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyShuffleOrder"/>.
    /// </summary>
    public static void ModifyShuffleOrder(CombatState combatState, Player player, List<CardModel> cards)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            model.ModifyShuffleOrder(player, cards);
        }
    }

    /// <summary>
    /// See <see cref="AbstractModel.TryModifyStarCost"/>.
    /// </summary>
    public static decimal ModifyStarCost(CombatState combatState, CardModel card, decimal originalCost)
    {
        // If the cost is < 0, it means it probably was already unplayable.
        if (originalCost < 0) return originalCost;

        decimal finalCost = originalCost;

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            model.TryModifyStarCost(card, finalCost, out finalCost);
        }

        return finalCost;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifySummonAmount"/>.
    /// </summary>
    public static decimal ModifySummonAmount(CombatState combatState, Player summoner, decimal amount, AbstractModel? source)
    {
        decimal finalAmount = amount;

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            finalAmount = model.ModifySummonAmount(summoner, finalAmount, source);
        }

        return finalAmount;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ModifyUnblockedDamageTarget"/>.
    /// </summary>
    public static Creature ModifyUnblockedDamageTarget(CombatState combatState, Creature originalTarget, decimal amount, ValueProp props, Creature? dealer)
    {
        Creature finalTarget = originalTarget;

        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            finalTarget = model.ModifyUnblockedDamageTarget(finalTarget, amount, props, dealer);
        }

        return finalTarget;
    }


    /// <summary>
    /// See <see cref="AbstractModel.ModifyUnknownMapPointRoomTypes"/>.
    /// </summary>
    public static IReadOnlySet<RoomType> ModifyUnknownMapPointRoomTypes(IClimbState climbState, IReadOnlySet<RoomType> roomTypes)
    {
        IReadOnlySet<RoomType> modifiedRoomTypes = new HashSet<RoomType>(roomTypes);

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            modifiedRoomTypes = model.ModifyUnknownMapPointRoomTypes(modifiedRoomTypes);
        }

        return modifiedRoomTypes;
    }

    #endregion

    #region Validation Hooks

    /// <summary>
    /// See <see cref="AbstractModel.ShouldAddToDeck"/>.
    /// </summary>
    public static bool ShouldAddToDeck(IClimbState climbState, CardModel card, out AbstractModel? preventer)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            if (!model.ShouldAddToDeck(card))
            {
                preventer = model;
                return false;
            }
        }

        preventer = null;
        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldAfflict"/>.
    /// </summary>
    public static bool ShouldAfflict(CombatState combatState, CardModel card, AfflictionModel affliction)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (!model.ShouldAfflict(card, affliction))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldAllowAncient"/>.
    /// </summary>
    public static bool ShouldAllowAncient(IClimbState climbState, Player player, AncientEventModel ancient)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            if (!model.ShouldAllowAncient(player, ancient))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldAllowHitting"/>.
    /// </summary>
    public static bool ShouldAllowHitting(CombatState combatState, Creature creature)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (!model.ShouldAllowHitting(creature))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldAllowMerchantCardRemoval"/>.
    /// </summary>
    public static bool ShouldAllowMerchantCardRemoval(IClimbState climbState, Player player)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            if (!model.ShouldAllowMerchantCardRemoval(player))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldAllowSelectingMoreCardRewards"/>.
    /// </summary>
    public static bool ShouldAllowSelectingMoreCardRewards(IClimbState climbState, Player player, CardReward reward)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            if (model.ShouldAllowSelectingMoreCardRewards(player, reward))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldAllowTargeting"/>.
    /// </summary>
    public static bool ShouldAllowTargeting(CombatState combatState, Creature target, out AbstractModel? preventer)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (!model.ShouldAllowTargeting(target))
            {
                preventer = model;
                return false;
            }
        }

        preventer = null;
        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldClearBlock"/>.
    /// </summary>
    public static bool ShouldClearBlock(CombatState combatState, Creature creature, out AbstractModel? preventer)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (!model.ShouldClearBlock(creature))
            {
                preventer = model;
                return false;
            }
        }

        preventer = null;
        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldCreatureBeRemovedFromCombatAfterDeath"/>.
    /// </summary>
    public static bool ShouldCreatureBeRemovedFromCombatAfterDeath(CombatState combatState, Creature creature)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (!model.ShouldCreatureBeRemovedFromCombatAfterDeath(creature))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldDie"/>.
    /// </summary>
    public static bool ShouldDie(IClimbState climbState, CombatState? combatState, Creature creature, out AbstractModel? preventer)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            if (!model.ShouldDie(creature))
            {
                preventer = model;
                return false;
            }
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            if (!model.ShouldDieLate(creature))
            {
                preventer = model;
                return false;
            }
        }

        preventer = null;
        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldDisableRemainingRestSiteOptions"/>.
    /// </summary>
    public static bool ShouldDisableRemainingRestSiteOptions(IClimbState climbState, Player player)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            if (!model.ShouldDisableRemainingRestSiteOptions(player))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldDraw"/>.
    /// </summary>
    public static bool ShouldDraw(CombatState combatState, Player player, bool fromHandDraw, out AbstractModel? modifier)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (!model.ShouldDraw(player, fromHandDraw))
            {
                modifier = model;
                return false;
            }
        }

        modifier = null;
        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldEtherealTrigger"/>.
    /// </summary>
    public static bool ShouldEtherealTrigger(CombatState combatState, CardModel card)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (!model.ShouldEtherealTrigger(card))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldFlush"/>.
    /// </summary>
    public static bool ShouldFlush(CombatState combatState, Player player)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (!model.ShouldFlush(player))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldGainGold"/>.
    /// </summary>
    public static bool ShouldGainGold(IClimbState climbState, CombatState? combatState, decimal amount, Player player)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            if (!model.ShouldGainGold(amount, player))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldGainStars"/>.
    /// </summary>
    public static bool ShouldGainStars(CombatState combatState, decimal amount, Player player)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (!model.ShouldGainStars(amount, player))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldPayExcessEnergyCostWithStars"/>.
    /// </summary>
    public static bool ShouldPayExcessEnergyCostWithStars(CombatState combatState, Player player)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (model.ShouldPayExcessEnergyCostWithStars(player))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldPlay"/>.
    /// </summary>
    public static bool ShouldPlay(CombatState combatState, CardModel card, out AbstractModel? preventer, AutoPlayType autoPlayType)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (!model.ShouldPlay(card, autoPlayType))
            {
                preventer = model;
                return false;
            }
        }

        preventer = null;
        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldPlayerResetEnergy"/>.
    /// </summary>
    public static bool ShouldPlayerResetEnergy(CombatState combatState, Player player)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (!model.ShouldPlayerResetEnergy(player))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldProceedToNextMapPoint"/>.
    /// </summary>
    public static bool ShouldProceedToNextMapPoint(IClimbState climbState)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            if (!model.ShouldProceedToNextMapPoint())
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldProcurePotion"/>.
    /// </summary>
    public static bool ShouldProcurePotion(IClimbState climbState, CombatState? combatState, PotionModel potion, Player player)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            if (!model.ShouldProcurePotion(potion, player))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldRefillMerchantEntry"/>.
    /// </summary>
    public static bool ShouldRefillMerchantEntry(IClimbState climbState, MerchantEntry entry, Player player)
    {
        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            if (model.ShouldRefillMerchantEntry(entry, player))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldStopCombatFromEnding"/>.
    /// </summary>
    public static bool ShouldStopCombatFromEnding(CombatState combatState)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (model.ShouldStopCombatFromEnding())
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldTakeExtraTurn"/>.
    /// </summary>
    public static bool ShouldTakeExtraTurn(CombatState combatState, Player player)
    {
        foreach (AbstractModel model in combatState.IterateHookListeners())
        {
            if (model.ShouldTakeExtraTurn(player))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// See <see cref="AbstractModel.ShouldForcePotionReward"/>.
    /// </summary>
    public static bool ShouldForcePotionReward(IClimbState climbState, Player player, RoomType roomType)
    {
        bool shouldForce = false;

        foreach (AbstractModel model in climbState.IterateHookListeners(null))
        {
            shouldForce = shouldForce || model.ShouldForcePotionReward(player, roomType);
        }

        return shouldForce;
    }

    public static bool ShouldPowerBeRemovedOnDeath(PowerModel power)
    {
        // NOTE: This is because CombatState ends up becoming null for player when combat ends
        if (power.Owner.CombatState == null) return true;

        foreach (AbstractModel model in power.CombatState.IterateHookListeners())
        {
            if (!model.ShouldPowerBeRemovedOnDeath(power))
            {
                return false;
            }
        }

        return true;
    }

    #endregion

    #region Private helper methods

    private static decimal ModifyDamageGiven(IClimbState climbState, CombatState? combatState, Creature? dealer, decimal amount, ValueProp props, Creature? target, CardModel? cardSource, ICollection<AbstractModel> modifiers)
    {
        decimal finalAmount = amount;

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageGivenEarly(dealer, finalAmount, props, target, cardSource);

            if ((int)prevAmount != (int)finalAmount)
            {
                modifiers.Add(model);
            }
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageGiven(dealer, finalAmount, props, target, cardSource);

            if ((int)prevAmount != (int)finalAmount)
            {
                modifiers.Add(model);
            }
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageGivenLate(dealer, finalAmount, props, target, cardSource);

            if ((int)prevAmount != (int)finalAmount)
            {
                modifiers.Add(model);
            }
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageGivenVeryLate(dealer, finalAmount, props, target, cardSource);

            if ((int)prevAmount != (int)finalAmount)
            {
                modifiers.Add(model);
            }
        }

        return finalAmount;
    }

    private static decimal ModifyDamageReceived(IClimbState climbState, CombatState? combatState, Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource, ICollection<AbstractModel> modifiers)
    {
        decimal finalAmount = amount;

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageReceivedEarly(target, finalAmount, props, dealer, cardSource);

            if ((int)prevAmount != (int)finalAmount)
            {
                modifiers.Add(model);
            }
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageReceived(target, finalAmount, props, dealer, cardSource);

            if ((int)prevAmount != (int)finalAmount)
            {
                modifiers.Add(model);
            }
        }

        foreach (AbstractModel model in climbState.IterateHookListeners(combatState))
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageReceivedLate(target, finalAmount, props, dealer, cardSource);

            if ((int)prevAmount != (int)finalAmount)
            {
                modifiers.Add(model);
            }
        }

        return finalAmount;
    }

    private static decimal ModifyEnergyCostInCombat(CardModel card, decimal originalCost, IEnumerable<AbstractModel> listeningModels)
    {
        // If the cost is < 0, it means it probably was already unplayable.
        if (originalCost < 0) return originalCost;

        decimal finalCost = originalCost;

        foreach (AbstractModel model in listeningModels)
        {
            model.TryModifyEnergyCostInCombat(card, finalCost, out finalCost);
        }

        return finalCost;
    }

    #endregion
}
