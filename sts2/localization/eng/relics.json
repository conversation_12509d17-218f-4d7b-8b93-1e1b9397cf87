{"AMETHYST_AUBERGINE.description": "[gold]Elites[/gold] drop an additional [blue]{Gold}[/blue] [gold]Gold[/gold] when defeated.", "AMETHYST_AUBERGINE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "AMETHYST_AUBERGINE.title": "<PERSON><PERSON>ys<PERSON>", "AMPHORAE.description": "Whenever you gain [gold]Block[/gold], gain [blue]{Block}[/blue] additional [gold]Block[/gold].", "AMPHORAE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "AMPHORAE.title": "Amphorae", "ANCHOR.description": "Start each combat with [blue]{Block}[/blue] [gold]Block[/gold].", "ANCHOR.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ANCHOR.title": "<PERSON><PERSON>", "ANCIENT_BOOSTER_PACK.description": "Upon pickup, choose [blue]1[/blue] of [blue]2[/blue] packs of cards to add to your [gold]Deck[/gold].", "ANCIENT_BOOSTER_PACK.eventDescription": "[red]Lose all Gold.[/red] Choose [blue]1[/blue] of [blue]2[/blue] packs of cards to add to your [gold]Deck[/gold].", "ANCIENT_BOOSTER_PACK.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ANCIENT_BOOSTER_PACK.title": "Ancient Booster Pack", "APEX_INSTINCT.description": "Choose [blue]{Cards}[/blue] [gold]{Cards:plural:Attack|Attacks}[/gold] in your [gold]Deck[/gold]. [gold]Enchant[/gold] them with [purple]Instinct[/purple].", "APEX_INSTINCT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "APEX_INSTINCT.title": "Apex Instinct", "ART_OF_WAR.description": "If you do not play any [gold]Attacks[/gold] during your turn, gain an additional {Energy:energyIcons()} next turn.", "ART_OF_WAR.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ART_OF_WAR.title": "Art of War", "ASTROLABE.description": "Upon pickup, choose and [gold]Transform[/gold] [blue]{Cards}[/blue] {Cards:plural:card|cards}, then [gold]Upgrade[/gold] them.", "ASTROLABE.eventDescription": "Choose and [gold]Transform[/gold] [blue]{Cards}[/blue] {Cards:plural:card|cards}, then [gold]Upgrade[/gold] them.", "ASTROLABE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ASTROLABE.title": "Astrolabe", "BAG_OF_MARBLES.description": "At the start of each combat, apply [blue]{Vulnerable}[/blue] [gold]Vulnerable[/gold] to ALL enemies.", "BAG_OF_MARBLES.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BAG_OF_MARBLES.title": "Bag of Marbles", "BAG_OF_PREPARATION.description": "At the start of each combat, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}.", "BAG_OF_PREPARATION.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BAG_OF_PREPARATION.title": "Bag of Preparation", "BEATING_REMNANT.description": "You cannot lose more than [blue]20[/blue] HP in a single turn.", "BEATING_REMNANT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BEATING_REMNANT.title": "Beating <PERSON>", "BEAUTIFUL_BRACELET.description": "Upon pickup, choose [blue]{Cards}[/blue] {Cards:plural:card|cards} in your [gold]Deck[/gold]. [gold]Enchant[/gold] them with [purple]Swift 3[/purple].", "BEAUTIFUL_BRACELET.eventDescription": "Choose [blue]{Cards}[/blue] {Cards:plural:card|cards} in your [gold]Deck[/gold]. [gold]Enchant[/gold] them with [purple]Swift {Swift}[/purple].", "BEAUTIFUL_BRACELET.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BEAUTIFUL_BRACELET.title": "Beautiful Bracelet", "BELLOWS.description": "The first [gold]Hand[/gold] you draw each combat is [gold]Upgraded[/gold].", "BELLOWS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BELLOWS.title": "Bellows", "BELL_OF_TWILIGHT.description": "Add [blue]{Elite}[/blue] extra [red]Elites[/red] to each map.", "BELL_OF_TWILIGHT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BELL_OF_TWILIGHT.title": "Bell of Twilight", "BELT_BUCKLE.description": "While you have no potions, you have [blue]{Dexterity}[/blue] additional [gold]Dexterity[/gold].", "BELT_BUCKLE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BELT_BUCKLE.title": "<PERSON>", "BENT_NAIL.description": "Whenever you receive unblocked attack damage, ALL enemies lose [blue]{Damage}[/blue] HP.", "BENT_NAIL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BENT_NAIL.title": "<PERSON><PERSON>", "BIG_HAT.description": "At the start of each combat, add [blue]{Cards}[/blue] random [gold]Ethereal[/gold] {Cards:plural:card|cards} to your [gold]Hand[/gold].", "BIG_HAT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BIG_HAT.title": "Big Hat", "BIG_MUSHROOM.description": "At the start of each combat, draw [blue]{Cards}[/blue] less {Cards:plural:card|cards}.", "BIG_MUSHROOM.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BIG_MUSHROOM.title": "Big Mushroom", "BLACK_BLOOD.description": "At the end of combat, heal [green]{Heal}[/green] HP.", "BLACK_BLOOD.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BLACK_BLOOD.title": "Black Blood", "BLACK_STAR.description": "[gold]Elites[/gold] drop an additional Relic when defeated.", "BLACK_STAR.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BLACK_STAR.title": "Black Star", "BLESSED_ANTLERS.description": "At the start of each [gold]Boss[/gold] and [gold]Elite[/gold] combat, gain [blue]{Regen}[/blue] [gold]Regen[/gold].", "BLESSED_ANTLERS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BLESSED_ANTLERS.title": "Blessed Antlers", "BLOOD_SOAKED_ROSE.description": "Gain {Energy:energyIcons()} at the start of each turn.", "BLOOD_SOAKED_ROSE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BLOOD_SOAKED_ROSE.title": "Blood-soaked Rose", "BLOOD_VIAL.description": "At the start of each combat, heal [green]{Heal}[/green] HP.", "BLOOD_VIAL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BLOOD_VIAL.title": "Blood Vial", "BONE_TEA.description": "At the start of the next {Combats:plural:combat|[blue]{}[/blue] combats}, [gold]Upgrade[/gold] your starting hand.", "BONE_TEA.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BONE_TEA.title": "Bone Tea", "BOOKMARK.description": "At the end of each turn, lower the cost of a random [gold]Retained[/gold] card by [blue]1[/blue] until played.", "BOOKMARK.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BOOKMARK.title": "Bookmark", "BOOK_OF_FIVE_RINGS.description": "Every [blue]{Cards}[/blue] {Cards:plural:card|cards} you add to your [gold]Deck[/gold], heal [green]{Heal}[/green] HP.", "BOOK_OF_FIVE_RINGS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BOOK_OF_FIVE_RINGS.title": "Book of Five Rings", "BOOK_REPAIR_KNIFE.description": "Whenever a non-Minion enemy dies to [gold]Doom[/gold], heal [blue]{Heal}[/blue] HP.", "BOOK_REPAIR_KNIFE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BOOK_REPAIR_KNIFE.title": "Book Repair Knife", "BOOMING_CONCH.description": "At the start of [gold]Elite[/gold] combats, gain [blue]{Strength}[/blue] [gold]Strength[/gold] and [blue]{Dexter<PERSON>}[/blue] [gold]Dexterity[/gold].", "BOOMING_CONCH.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BOOMING_CONCH.title": "Booming <PERSON>", "BOUND_PHYLACTERY.description": "At the start of your turn, [gold]Summon[/gold] [blue]{<PERSON>mm<PERSON>}[/blue].", "BOUND_PHYLACTERY.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BOUND_PHYLACTERY.title": "Bound Phylactery", "BREAD.description": "Gain {GainEnergy:energyIcons()} at the start of each turn. At the start of your first turn, lose {LoseEnergy:energyIcons()}.", "BREAD.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BREAD.title": "Bread", "BRILLIANT_SCARF.description": "The [blue]5th[/blue] card you play each turn is free.", "BRILLIANT_SCARF.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BRILLIANT_SCARF.title": "Brilliant Scarf", "BRIMSTONE.description": "At the start of your turn, gain [blue]{SelfStrength}[/blue] [gold]Strength[/gold] and ALL enemies gain [blue]{EnemyStrength}[/blue] [gold]Strength[/gold].", "BRIMSTONE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BRIMSTONE.title": "Brimstone", "BROKEN_CHAIN.description": "Upon pickup, remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold]. Add [red]Folly[/red] to your [gold]Deck[/gold].", "BROKEN_CHAIN.eventDescription": "Remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold]. [red]Add Folly to your Deck.[/red]", "BROKEN_CHAIN.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BROKEN_CHAIN.title": "Broken Chain", "BROKEN_ORBS.description": "Start the next {Uses:plural:combat|[blue]{}[/blue] combats} with an additional {Energy:energyIcons()}.", "BROKEN_ORBS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BROKEN_ORBS.title": "Broken Orbs", "BURNING_BLOOD.description": "At the end of combat, heal [green]{Heal}[/green] HP.", "BURNING_BLOOD.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BURNING_BLOOD.title": "Burning Blood", "BURNING_STICKS.description": "The first time each combat you [gold]Exhaust[/gold] a card, add a copy of it to your [gold]Hand[/gold].", "BURNING_STICKS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BURNING_STICKS.title": "Burning Sticks", "BYRDPIP.description": "Upon pickup, gain the card [gold]<PERSON> Swoop[/gold]. A [gold]Byrdpip[/gold] will accompany you in battles.", "BYRDPIP.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "BYRDPIP.title": "Byrdpip", "CALLING_BELL.description": "Upon pickup, obtain a unique [red]Curse[/red] and [blue]{Relics}[/blue] [gold]Relics[/gold].", "CALLING_BELL.eventDescription": "Obtain a unique [gold]Curse[/gold] and [blue]{Relics}[/blue] [gold]Relics[/gold].", "CALLING_BELL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CALLING_BELL.title": "Calling Bell", "CAPTAINS_WHEEL.description": "At the start of your [blue]3rd[/blue] turn, gain [blue]{Block}[/blue] [gold]Block[/gold].", "CAPTAINS_WHEEL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CAPTAINS_WHEEL.title": "Captain's Wheel", "CARNIVOROUS_TERRARIUM.description": "Whenever you kill an [gold]Elite[/gold], [gold]Upgrade[/gold] [blue]{Cards}[/blue] random {Cards:plural:card|cards}.", "CARNIVOROUS_TERRARIUM.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CARNIVOROUS_TERRARIUM.title": "Carnivorous Terrarium", "CAULDRON.description": "Upon pickup, brews [blue]{Potions}[/blue] random {Potions:plural:potion|potions}.", "CAULDRON.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CAULDRON.title": "<PERSON><PERSON><PERSON>", "CENTENNIAL_PUZZLE.description": "The first time you lose HP each combat, draw [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "CENTENNIAL_PUZZLE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CENTENNIAL_PUZZLE.title": "Centennial Puzzle", "CHAIN_LINKS.description": "At the start of each combat, gain [blue]{Plating}[/blue] [gold]Plating[/gold].", "CHAIN_LINKS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CHAIN_LINKS.title": "Chain Links", "CHARONS_ASHES.description": "Whenever you [gold]Exhaust[/gold] a card, deal [blue]{Damage}[/blue] damage to ALL enemies.", "CHARONS_ASHES.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CHARONS_ASHES.title": "<PERSON><PERSON>'s Ashes", "CHOICES_PARADOX.description": "At the start of each combat, add [blue]1[/blue] of [blue]{Cards}[/blue] random cards to your [gold]Hand[/gold]. Add [gold]Retain[/gold] to the chosen card.", "CHOICES_PARADOX.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CHOICES_PARADOX.selectionScreenPrompt": "Select a Card to Add to Your [gold]Hand[/gold].", "CHOICES_PARADOX.title": "Choices Paradox", "CHOSEN_CHEESE.description": "At the end of combat, gain [blue]{MaxHp}[/blue] Max HP.", "CHOSEN_CHEESE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CHOSEN_CHEESE.title": "The Chosen Cheese", "CIRCLET.description": "It's a circlet", "CIRCLET.flavor": "A curious relic which appears when there's a problem within the [gold]Spire[/gold] or there are no more relics to discover.", "CIRCLET.title": "Circlet", "CLERICS_HEADPIECE.description": "Upon pickup, choose [blue]1[/blue] of [blue]3[/blue] [gold]Multiplayer Colorless Cards[/gold] to add to your [gold]Deck[/gold].", "CLERICS_HEADPIECE.eventDescription": "Choose [blue]1[/blue] of [blue]3[/blue] [gold]Multiplayer Colorless Cards[/gold] to add to your [gold]Deck[/gold].", "CLERICS_HEADPIECE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CLERICS_HEADPIECE.title": "<PERSON><PERSON><PERSON>'s Headpiece", "CLOCKWORK_SOUVENIR.description": "Start each combat with [blue]{Artifact}[/blue] [gold]Artifact[/gold].", "CLOCKWORK_SOUVENIR.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CLOCKWORK_SOUVENIR.title": "Clockwork Souvenir", "CRACKED_CORE.description": "At the start of each combat, [gold]Channel[/gold] [blue]{Lightning}[/blue] [gold]Lightning[/gold].", "CRACKED_CORE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CRACKED_CORE.title": "Cracked Core", "CRIMSON_PENDANT.description": "Upon pickup, add [blue]{Cards}[/blue] [gold]{Cards:plural:Apparition|Apparitions}[/gold] to your [gold]Deck[/gold].", "CRIMSON_PENDANT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CRIMSON_PENDANT.title": "Crimson Pendant", "CURSED_KETTLE.description": "Whenever you defeat an [gold]Elite[/gold], the other [gold]Elites[/gold] in the act will gain [blue]{Strength}[/blue] [gold]Strength[/gold] at the start of combat.", "CURSED_KETTLE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "CURSED_KETTLE.title": "Cursed <PERSON>", "DARKSTONE_PERIAPT.description": "Whenever you obtain a [red]Curse[/red], raise your Max HP by [blue]{MaxHp}[/blue].", "DARKSTONE_PERIAPT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "DARKSTONE_PERIAPT.title": "Darkstone Periapt", "DATA_DISK.description": "Start each combat with [blue]{Focus}[/blue] [gold]Focus[/gold].", "DATA_DISK.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "DATA_DISK.title": "Data Disk", "DAUGHTER_OF_THE_WIND.description": "Whenever you play an [gold]Attack[/gold], gain [blue]{Block}[/blue] [gold]Block[/gold].", "DAUGHTER_OF_THE_WIND.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "DAUGHTER_OF_THE_WIND.title": "Daughter of the Wind", "DEPRECATED_RELIC.description": "This relic is no longer in the game. It's just here for legacy purposes.", "DEPRECATED_RELIC.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "DEPRECATED_RELIC.title": "Deprecated Relic", "DINGY_RUG.description": "Card rewards can now contain Colorless cards.", "DINGY_RUG.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "DINGY_RUG.title": "Dingy Rug", "DISCOVERY_TOTEM.DEFECT.title": "Gear Totem", "DISCOVERY_TOTEM.IRONCLAD.title": "Demon Totem", "DISCOVERY_TOTEM.NECROBINDER.title": "Lich Totem", "DISCOVERY_TOTEM.REGENT.title": "<PERSON>", "DISCOVERY_TOTEM.SILENT.title": "Venom Totem", "DISCOVERY_TOTEM.description": "See [blue]{Cards}[/blue] {Character.StringValue:cond:cards from [gold]{}[/gold]|from another character}. Choose any number of them to add to your [gold]Deck[/gold].", "DISCOVERY_TOTEM.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "DISCOVERY_TOTEM.selectionScreenPrompt": "Select Any Number of Cards to Add to Your Deck.", "DISCOVERY_TOTEM.title": "Discovery Totem", "DOLLYS_MIRROR.description": "Upon pickup, obtain an additional copy of a card in your [gold]Deck[/gold].", "DOLLYS_MIRROR.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "DOLLYS_MIRROR.selectionScreenPrompt": "Select [blue]1[/blue] card to [gold]Copy[/gold]", "DOLLYS_MIRROR.title": "Dolly's Mirror", "DRAGON_FRUIT.description": "Whenever you gain [gold]Gold[/gold], raise your Max HP by [blue]{MaxHp}[/blue].", "DRAGON_FRUIT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "DRAGON_FRUIT.title": "Dragon Fruit", "DREAM_CATCHER.additionalRestSiteHealText": "Add a card to your [gold]Deck[/gold] from Dream Catcher.", "DREAM_CATCHER.description": "Whenever you [gold]Rest[/gold], you may add a card to your [gold]Deck[/gold].", "DREAM_CATCHER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "DREAM_CATCHER.title": "Dream Catcher", "DUSTY_TOME.description": "Upon pickup, obtain {AncientCard.StringValue:cond:[gold]{}[/gold]|an [gold]Ancient Card[/gold]}.", "DUSTY_TOME.eventDescription": "Obtain {AncientCard.StringValue:cond:[gold]{}[/gold]|an [gold]Ancient Card[/gold]}.", "DUSTY_TOME.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "DUSTY_TOME.title": "<PERSON>", "DUST_BUNNY.description": "Whenever you shuffle your [gold]Draw Pile[/gold], add a [gold]Soot[/gold] to your [gold]Draw Pile[/gold].", "DUST_BUNNY.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "DUST_BUNNY.title": "<PERSON> Bunny", "DYBBUK_CUBE.description": "Each combat, the first time you play a card that [gold]Debuffs[/gold] an enemy, double its effect.", "DYBBUK_CUBE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "DYBBUK_CUBE.title": "Dybbuk Cube", "ECHO_CHAMBER.description": "The first time you play a [gold]Power[/gold] each combat, shuffle [blue]{Cards}[/blue] {Cards:plural:copy|copies} of it into your [gold]Draw Pile[/gold].", "ECHO_CHAMBER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ECHO_CHAMBER.title": "Echo Chamber", "ECTOPLASM.description": "You can no longer gain [gold]Gold[/gold]. Gain {Energy:energyIcons()} at the start of your turn.\n", "ECTOPLASM.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ECTOPLASM.title": "Ectoplasm", "ELECTRIFIED_SHARD.description": "Upon pickup, [gold]Enchant[/gold] a [gold]Skill[/gold] with [gold]Imbued[/gold].", "ELECTRIFIED_SHARD.eventDescription": "[gold]Enchant[/gold] a [gold]Skill[/gold] with [gold]Imbued[/gold].", "ELECTRIFIED_SHARD.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ELECTRIFIED_SHARD.title": "Electrified <PERSON>hard", "EMBER_TEA.description": "At the start of the next {Combats:plural:combat|[blue]{}[/blue] combats}, gain [blue]{Strength}[/blue] [gold]Strength[/gold].", "EMBER_TEA.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "EMBER_TEA.title": "Ember Tea", "EMOTION_CHIP.description": "If you lost HP during the previous turn, trigger the passive ability of all Orbs at the start of your turn.", "EMOTION_CHIP.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "EMOTION_CHIP.title": "Emotion Chip", "EMPTY_CAGE.description": "Upon pickup, remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold].", "EMPTY_CAGE.eventDescription": "Remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold].", "EMPTY_CAGE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "EMPTY_CAGE.title": "Empty Cage", "ENDLESS_APPETITE.description": "Whenever you [gold]Exhaust[/gold] a card, gain [blue]{Strength}[/blue] [gold]Strength[/gold].", "ENDLESS_APPETITE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ENDLESS_APPETITE.title": "Endless Appetite", "ENORMOUS_RING.description": "Whenever [gold]Summon[/gold] revives [gold]Osty[/gold], gain [blue]{Block}[/blue] [gold]Block[/gold].", "ENORMOUS_RING.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ENORMOUS_RING.title": "Enormous Ring", "ETERNAL_FEATHER.description": "For every {Cards:plural:card|[blue]{}[/blue] cards} in your [gold]Deck[/gold], heal [green]{Heal}[/green] HP whenever you enter a Rest Site.", "ETERNAL_FEATHER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ETERNAL_FEATHER.title": "Eternal Feather", "FABLE.description": "Whenever you add a card to your [gold]Deck[/gold], add one additional copy.", "FABLE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "FABLE.title": "Fable", "FENCING_MANUAL.description": "Whenever you play [gold]Sovereign Blade[/gold], [gold]Forge[/gold] [blue]{Forge}[/blue].", "FENCING_MANUAL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "FENCING_MANUAL.title": "Fencing Manual", "FOREVER_ICE.description": "The first time you play a [gold]Power[/gold] each combat, gain [blue]{Block}[/blue] [gold]Block[/gold].", "FOREVER_ICE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "FOREVER_ICE.title": "Forever Ice", "FRAGRANT_MUSHROOM.description": "Upon pickup, [gold]Upgrade[/gold] [blue]{Cards}[/blue] random {Cards:plural:card|cards}.", "FRAGRANT_MUSHROOM.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "FRAGRANT_MUSHROOM.title": "Fragrant Mushroom", "FROZEN_EGG.description": "Whenever you add a [gold]Power[/gold] card into your [gold]Deck[/gold], [gold]Upgrade[/gold] it.", "FROZEN_EGG.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "FROZEN_EGG.title": "Frozen Egg", "FUNERARY_MASK.description": "At the start of each combat, add [blue]{Cards}[/blue] [gold]{Cards:plural:Soul|Souls}[/gold] to your [gold]Draw Pile[/gold].", "FUNERARY_MASK.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "FUNERARY_MASK.title": "Funerary Mask", "FUR_COAT.description": "Mark [blue]{Combats}[/blue] random combats. Enemies in those rooms have [blue]1[/blue] HP.", "FUR_COAT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "FUR_COAT.title": "Fur Coat", "FYSHING_FLOAT.description": "Whenever you add a card that gains [gold]Block[/gold] to your [gold]Deck[/gold], [gold]Enchant[/gold] it with [purple]Nimble[/purple] [blue]{NimbleAmount}[/blue].", "FYSHING_FLOAT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "FYSHING_FLOAT.title": "Fyshing Float", "GALACTIC_DUST.description": "At the start of each combat, gain {Stars:starIcons()}.", "GALACTIC_DUST.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "GALACTIC_DUST.title": "Galactic Dust", "GAMBLING_CHIP.description": "At the start of each combat, discard any number of cards then draw that many.", "GAMBLING_CHIP.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "GAMBLING_CHIP.selectionScreenPrompt": "Choose Any Number of Cards to Replace", "GAMBLING_CHIP.title": "Gambling Chip", "GAME_PIECE.description": "Whenever you play a [gold]Power[/gold], draw [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "GAME_PIECE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "GAME_PIECE.title": "Game Piece", "GHOST_SEED.description": "[gold]Strikes[/gold] and [gold]Defends[/gold] gain [gold]Ethereal[/gold].", "GHOST_SEED.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "GHOST_SEED.title": "<PERSON> Seed", "GIFT_BOX.description": "Upon pickup, obtain [blue]{Relics}[/blue] random [gold]{Relics:plural:Relic|Relics}[/gold]. Add an additional [gold]Strike[/gold] and [gold]Defend[/gold] to your [gold]Deck[/gold].", "GIFT_BOX.eventDescription": "Obtain [blue]{Relics}[/blue] random [gold]{Relics:plural:Relic|Relics}[/gold]. [red]Add an additional[/red] [gold]Strike[/gold] [red]and[/red] [gold]Defend[/gold] [red]to your[/red] [gold]Deck[/gold][red].[/red]", "GIFT_BOX.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "GIFT_BOX.title": "Gift Box", "GIRYA.description": "You can now gain [gold]Strength[/gold] at [gold]Rest Sites[/gold]. ([blue]3[/blue] times max)", "GIRYA.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "GIRYA.title": "<PERSON><PERSON><PERSON>", "GLAMOROUS_BROOCH.description": "[gold]Enchant[/gold] all card rewards with [purple]Glam[/purple].", "GLAMOROUS_BROOCH.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "GLAMOROUS_BROOCH.title": "Glamorous Brooch", "GLASS_EYE.description": "Upon pickup, obtain [blue]2[/blue] [gold]Common[/gold] cards, [blue]2[/blue] [gold]Uncommon[/gold] cards, and [blue]1[/blue] [gold]Rare[/gold] card.", "GLASS_EYE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "GLASS_EYE.title": "Glass Eye", "GLOWING_ORB.description": "Start [gold]Elite[/gold] combats with an additional {Energy:energyIcons()}.", "GLOWING_ORB.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "GLOWING_ORB.title": "Glowing Orb", "GNARLED_HAMMER.description": "[gold]Enchant[/gold] up to [blue]{Cards}[/blue] {Cards:plural:Attack|Attacks} with [purple]Sharp {SharpAmount:diff()}[/purple].", "GNARLED_HAMMER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "GNARLED_HAMMER.title": "Gnarled Hammer", "GOLDEN_COMPASS.description": "Replaces the [gold]Act[/gold] [blue]2[/blue] Map with a single special path.", "GOLDEN_COMPASS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "GOLDEN_COMPASS.title": "Golden Compass", "GOLDEN_IDOL.description": "Upon pickup, receive [red]Greed[/red]. Gain [blue]{Gold}[/blue] [gold]Gold[/gold].", "GOLDEN_IDOL.eventDescription": "Receive [red]Greed[/red]. Gain [blue]{Gold}[/blue] [gold]Gold[/gold].", "GOLDEN_IDOL.flavor": "PLACEHOLDER", "GOLDEN_IDOL.title": "Golden Idol", "GOLD_PLATED_CABLES.description": "Your rightmost Orb triggers its passive an additional time.", "GOLD_PLATED_CABLES.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "GOLD_PLATED_CABLES.title": "Gold-Plated Cables", "GREMLIN_HORN.description": "Whenever an enemy dies, gain {Energy:energyIcons()} and draw [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "GREMLIN_HORN.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "GREMLIN_HORN.title": "Gremlin Horn", "HAND_DRILL.description": "Whenever you break an enemy's [gold]Block[/gold], apply [blue]{Vulnerable}[/blue] [gold]Vulnerable[/gold].", "HAND_DRILL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "HAND_DRILL.title": "<PERSON> Drill", "HAPPY_FLOWER.description": "Every [blue]{Turns}[/blue] {Turns:plural:turn|turns}, gain {Energy:energyIcons()}.", "HAPPY_FLOWER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "HAPPY_FLOWER.title": "<PERSON> Flower", "HEAP_OF_COALS.description": "[gold]Enchant[/gold] all [gold]Strikes[/gold] in your [gold]Deck[/gold] with [purple]Tezcatara's Ember[/purple].", "HEAP_OF_COALS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "HEAP_OF_COALS.title": "<PERSON><PERSON> of Coals", "HELICAL_DART.description": "Whenever you play a [gold]Shiv[/gold], gain [blue]{Dexterity}[/blue] [gold]Dexterity[/gold] this turn.", "HELICAL_DART.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "HELICAL_DART.title": "Helical Dart", "HISTORY_COURSE.description": "At the start of your turn, play a copy of your last played card.", "HISTORY_COURSE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "HISTORY_COURSE.title": "History Course", "HORN_CLEAT.description": "At the start of your [blue]2nd[/blue] turn, gain [blue]{Block}[/blue] [gold]Block[/gold].", "HORN_CLEAT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "HORN_CLEAT.title": "<PERSON>", "HUNGERING_BELLY.description": "Whenever a non-minion enemy dies, heal [green]{Heal}[/green] HP.", "HUNGERING_BELLY.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "HUNGERING_BELLY.title": "Hungering Belly", "HUNGERING_PORTRAIT.description": "At the start of your turn, [gold]Exhaust[/gold] the top card of your [gold]Draw Pile[/gold] and gain [blue]{Strength}[/blue] [gold]Strength[/gold].", "HUNGERING_PORTRAIT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "HUNGERING_PORTRAIT.title": "Hungering Portrait", "ICE_CREAM.description": "Energy is now conserved between turns.", "ICE_CREAM.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ICE_CREAM.title": "Ice Cream", "INFUSED_CORE.description": "At the start of each combat, [gold]Channel[/gold] [blue]{Lightning}[/blue] [gold]Lightning[/gold].", "INFUSED_CORE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "INFUSED_CORE.title": "Infused Core", "INK_BOTTLE.description": "Every time you play [blue]{CardThreshold}[/blue] {CardThreshold:plural:card|cards}, draw [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "INK_BOTTLE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "INK_BOTTLE.title": "Ink Bottle", "INTIMIDATING_HELMET.description": "Whenever you play a card that costs {Energy:energyIcons()} or more, gain [blue]{Block}[/blue] [gold]Block[/gold].", "INTIMIDATING_HELMET.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "INTIMIDATING_HELMET.title": "Intimidating Helmet", "JEWELRY_BOX.description": "Upon pickup, choose [blue]1[/blue] of [blue]{Relics}[/blue] [gold]Relics[/gold] to obtain.", "JEWELRY_BOX.eventDescription": "Choose [blue]1[/blue] of [blue]{Relics}[/blue] [gold]Relics[/gold] to obtain.", "JEWELRY_BOX.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "JEWELRY_BOX.title": "Jewelry Box", "JOSS_PAPER.description": "Every [blue]{ExhaustAmount}[/blue] times you [gold]Exhaust[/gold] a card, draw [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "JOSS_PAPER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "JOSS_PAPER.title": "Joss Paper", "JUZU_BRACELET.description": "Regular enemy combats are no longer encountered in [gold]?[/gold] rooms.", "JUZU_BRACELET.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "JUZU_BRACELET.title": "Juzu Bracelet", "KALEIDOSCOPE.description": "Upon pickup, [gold]Upgrade[/gold] [blue]{Cards}[/blue] random {Cards:plural:card|cards}.", "KALEIDOSCOPE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "KALEIDOSCOPE.title": "Kaleidoscope", "KIFUDA.description": "[gold]Enchant[/gold] up to [blue]{Cards}[/blue] {Cards:plural:card|cards} with [purple]Adroit[/purple].", "KIFUDA.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "KIFUDA.title": "<PERSON><PERSON><PERSON>", "KUNAI.description": "Every time you play [blue]{Cards}[/blue] [gold]Attacks[/gold] in a single turn, gain [blue]{Dexterity}[/blue] [gold]Dexterity[/gold].", "KUNAI.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "KUNAI.title": "Kunai", "KUSARIGAMA.description": "Every time you play [blue]{Cards}[/blue] [gold]Skills[/gold], gain [blue]{Block}[/blue] [gold]Block[/gold].", "KUSARIGAMA.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "KUSARIGAMA.title": "Ku<PERSON><PERSON><PERSON>", "LANTERN.description": "Start each combat with an additional {Energy:energyIcons()}.", "LANTERN.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LANTERN.title": "Lantern", "LASTING_CANDY.description": "Every other combat, your card rewards gain an additional [gold]Power[/gold] card.", "LASTING_CANDY.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LASTING_CANDY.title": "Lasting <PERSON>", "LAUGHING_STONE.description": "Whenever you lose HP from a card, gain {Energy:energyIcons()}.", "LAUGHING_STONE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LAUGHING_STONE.title": "<PERSON>", "LEAD_PAPERWEIGHT.description": "Upon pickup, choose [blue]1[/blue] of [blue]3[/blue] [gold]Colorless Cards[/gold] to add to your [gold]Deck[/gold].", "LEAD_PAPERWEIGHT.eventDescription": "Choose [blue]1[/blue] of [blue]3[/blue] [gold]Colorless Cards[/gold] to add to your [gold]Deck[/gold].", "LEAD_PAPERWEIGHT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LEAD_PAPERWEIGHT.title": "Lead Paperweight", "LEES_WAFFLE.description": "Raise your Max HP by [blue]{MaxHp}[/blue] and heal all of your HP.", "LEES_WAFFLE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LEES_WAFFLE.title": "<PERSON>'s Waffle", "LETTER_OPENER.description": "Every time you play [blue]{Cards}[/blue] [gold]Skills[/gold] in a single turn, deal [blue]{Damage}[/blue] damage to ALL enemies.", "LETTER_OPENER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LETTER_OPENER.title": "Letter Opener", "LIDAR.description": "At the start of each combat, add [blue]{Cards}[/blue] zero-cost cards from your [gold]Draw Pile[/gold] to your [gold]Hand[/gold]", "LIDAR.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LIDAR.title": "Lidar", "LIGHT_BULB.description": "At the end of combat, [gold]Upgrade[/gold] all card rewards if you took no damage.", "LIGHT_BULB.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LIGHT_BULB.title": "Light Bulb", "LITTLE_GHOST.description": "Create an [gold]Ethereal[/gold] copy of the first [gold]Attack[/gold] you play each turn.", "LITTLE_GHOST.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LITTLE_GHOST.title": "Little Ghost", "LIZARD_TAIL.description": "When you would die, heal to [green]{Heal}%[/green] of your Max HP instead (works once).", "LIZARD_TAIL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LIZARD_TAIL.title": "<PERSON>rd <PERSON>l", "LOOMING_FRUIT.description": "Upon pickup, raise your Max HP by [blue]{MaxHp}[/blue].", "LOOMING_FRUIT.eventDescription": "Raise your Max HP by [blue]{MaxHp}[/blue].", "LOOMING_FRUIT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LOOMING_FRUIT.title": "Looming Fruit", "LOST_SOUL.description": "At the start of each combat, raise your Max HP by [blue]{MaxHp}[/blue].", "LOST_SOUL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LOST_SOUL.title": "Lost Soul", "LOST_WISP.description": "At the start of your turn, deal [blue]{Damage}[/blue] damage to ALL enemies.", "LOST_WISP.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LOST_WISP.title": "Lost Wisp", "LUCKY_FYSH.description": "Whenever you add a card to your [gold]Deck[/gold], gain [blue]{Gold}[/blue] [gold]Gold[/gold].", "LUCKY_FYSH.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LUCKY_FYSH.title": "<PERSON>", "LUNAR_PASTRY.description": "At the end of your turn, gain {Stars:starIcons()}.", "LUNAR_PASTRY.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "LUNAR_PASTRY.title": "Lunar Pastry", "MAGIC_POT.description": "Remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold]. After each combat, randomly add [blue]1[/blue] back [gold]Upgraded[/gold].{CardTitles.StringValue:cond:\n\nCards in the Pot:\n{}|}", "MAGIC_POT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MAGIC_POT.title": "Magic Pot", "MANGO.description": "Upon pickup, raise your Max HP by [blue]{MaxHp}[/blue].", "MANGO.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MANGO.title": "Mango", "MASTERS_TEACHINGS.description": "Whenever you play a card that costs {EnergyThreshold:energyIcons()} or more, gain {Energy:energyIcons()}.", "MASTERS_TEACHINGS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MASTERS_TEACHINGS.title": "Master's Teachings", "MATCHBOX.description": "Start each combat with an additional {Energy:energyIcons()}.", "MATCHBOX.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MATCHBOX.title": "Matchbox", "MAW_BANK.description": "Whenever you climb a floor, gain [blue]{Gold}[/blue] [gold]Gold[/gold]. No longer works when you spend any [gold]Gold[/gold] at the shop.", "MAW_BANK.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MAW_BANK.title": "Maw Bank", "MEAL_TICKET.description": "Whenever you enter a shop room, heal [green]{Heal}[/green] HP.", "MEAL_TICKET.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MEAL_TICKET.title": "Meal Ticket", "MEAT_ON_THE_BONE.description": "If your HP is at or below [blue]{HpT<PERSON>esh<PERSON>}%[/blue] at the end of combat, heal [green]{Heal}[/green] HP.", "MEAT_ON_THE_BONE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MEAT_ON_THE_BONE.title": "Meat on the Bone", "MEDICAL_KIT.description": "[gold]Status[/gold] cards can now be played. Playing a Status will [gold]Exhaust[/gold] the card.", "MEDICAL_KIT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MEDICAL_KIT.title": "Medical Kit", "MEMBERSHIP_CARD.description": "[blue]{Discount}%[/blue] discount on all products!", "MEMBERSHIP_CARD.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MEMBERSHIP_CARD.title": "Membership Card", "MERCHANTS_ROBES.description": "When you encounter the [gold]Merchant[/gold], immediately obtain EVERYTHING he sells.", "MERCHANTS_ROBES.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MERCHANTS_ROBES.title": "Merchant's Robes", "MERCURY_HOURGLASS.description": "At the start of your turn, deal [blue]{Damage}[/blue] damage to ALL enemies.", "MERCURY_HOURGLASS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MERCURY_HOURGLASS.title": "Mercury Hourglass", "METRONOME.description": "The first time you [gold]Channel[/gold] [blue]{OrbCount}[/blue] [gold]Orbs[/gold] each combat, deal [blue]{Damage}[/blue] damage to ALL enemies.", "METRONOME.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "METRONOME.title": "Metronome", "MINIATURE_LAB.description": "Gain [blue]{PotionSlots}[/blue] potion slots filled with random potions.", "MINIATURE_LAB.eventDescription": "Upon pickup, gain [blue]{PotionSlots}[/blue] potion {PotionSlots:plural:slot|slots} filled with {PotionSlots:plural:a random potion|random potions}.", "MINIATURE_LAB.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MINIATURE_LAB.title": "Miniature Lab", "MINI_REGENT.description": "The first time you spend {singleStarIcon} each turn, gain {Energy:energyIcons()}.", "MINI_REGENT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MINI_REGENT.title": "Mini Regent", "MOLTEN_EGG.description": "Whenever you add an [gold]Attack[/gold] card to your [gold]Deck[/gold], [gold]Upgrade[/gold] it.", "MOLTEN_EGG.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MOLTEN_EGG.title": "Molten Egg", "MR_STRUGGLES.description": "At the start of your turn, deal damage equal to the turn number to ALL enemies.", "MR_STRUGGLES.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MR_STRUGGLES.title": "Mr. <PERSON>", "MUMMIFIED_HAND.description": "Whenever you play a [gold]Power[/gold], a random card in your [gold]Hand[/gold] is free to play that turn.", "MUMMIFIED_HAND.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MUMMIFIED_HAND.title": "Mummified Hand", "MYSTERIOUS_COCOON.description": "Upon gaining [blue]{Experience}[/blue] experience, transform [gold]Training Strike[/gold] into [gold]Impale[/gold].", "MYSTERIOUS_COCOON.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MYSTERIOUS_COCOON.title": "Mysterious Cocoon", "MYSTERY_MACHINE.description": "Upon pickup, add [blue]{Unknown}[/blue] extra [gold]?[/gold]s to the map.", "MYSTERY_MACHINE.eventDescription": "Add [blue]{Unknown}[/blue] extra [gold]?[/gold] rooms to the map.", "MYSTERY_MACHINE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MYSTERY_MACHINE.title": "Mystery Machine", "MYSTIC_LIGHTER.description": "[gold]Enchanted[/gold] [gold]Attacks[/gold] deal [blue]{Damage}[/blue] additional damage.", "MYSTIC_LIGHTER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MYSTIC_LIGHTER.title": "Mystic Lighter", "MY_MANTLE.description": "For every [blue]{Stars}[/blue] {singleStarIcon} spent, gain [blue]{Block}[/blue] [gold]Block[/gold].", "MY_MANTLE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "MY_MANTLE.title": "My Mantle", "NEOWS_NOTE.description": "Upon pickup, gain [blue]{Gold}[/blue] [gold]Gold[/gold].", "NEOWS_NOTE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "NEOWS_NOTE.title": "<PERSON><PERSON>'s Note", "NINJA_SCROLL.description": "At the start of each combat, add [blue]{Shivs}[/blue] [gold]{Shivs:plural:Shiv|Shivs}[/gold] to your [gold]Hand[/gold].", "NINJA_SCROLL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "NINJA_SCROLL.title": "<PERSON>", "NUNCHAKU.description": "Every time you play [blue]{Cards}[/blue] [gold]Attacks[/gold], gain {Energy:energyIcons()}.", "NUNCHAKU.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "NUNCHAKU.title": "<PERSON><PERSON><PERSON><PERSON>", "NYE_SHROUD.description": "At the start of your [blue]3rd[/blue] turn, gain {Energy:energyIcons()}.", "NYE_SHROUD.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "NYE_SHROUD.title": "<PERSON><PERSON>", "ODDLY_HEAVY_STONE.description": "The first time you gain [gold]Block[/gold] from a card each turn, double the amount gained.", "ODDLY_HEAVY_STONE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ODDLY_HEAVY_STONE.title": "Oddly Heavy Stone", "ODDLY_SMOOTH_STONE.description": "Start each combat with [blue]{Dexter<PERSON>}[/blue] [gold]Dexterity[/gold].", "ODDLY_SMOOTH_STONE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ODDLY_SMOOTH_STONE.title": "<PERSON><PERSON>", "OLD_COIN.description": "Upon pickup, gain [blue]{Gold}[/blue] [gold]Gold[/gold].", "OLD_COIN.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "OLD_COIN.title": "Old Coin", "ORACLE_BONE.description": "The first card you play each combat is played an extra time.", "ORACLE_BONE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ORACLE_BONE.title": "Oracle Bone", "ORANGE_DOUGH.description": "At the start of each combat, add [blue]{Cards}[/blue] random Colorless {Cards:plural:card|cards} to your [gold]Hand[/gold].", "ORANGE_DOUGH.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ORANGE_DOUGH.title": "Orange Dough", "ORICHALCUM.description": "If you end your turn without [gold]Block[/gold], gain [blue]{Block}[/blue] [gold]Block[/gold].", "ORICHALCUM.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ORICHALCUM.title": "Orichalcum", "ORNAMENTAL_FAN.description": "Every time you play [blue]{Cards}[/blue] [gold]Attacks[/gold] in a single turn, gain [blue]{Block}[/blue] [gold]Block[/gold].", "ORNAMENTAL_FAN.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ORNAMENTAL_FAN.title": "Ornamental Fan", "ORRERY.description": "Upon pickup, choose and add [blue]{Cards}[/blue] {Cards:plural:card|cards} to your [gold]Deck[/gold].", "ORRERY.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ORRERY.title": "<PERSON><PERSON><PERSON>", "PAELS_CLAW.description": "[gold]Enchant[/gold] [blue]{Cards}[/blue] {Cards:plural:card|cards} with [purple]{EnchantmentName}[/purple].", "PAELS_CLAW.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PAELS_CLAW.title": "<PERSON><PERSON>'s Claw", "PAELS_EYE.description": "The first time each combat you end your turn without playing cards, [gold]Exhaust[/gold] your [gold]Hand[/gold], and take an extra turn.", "PAELS_EYE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PAELS_EYE.title": "<PERSON><PERSON>'s Eye", "PAELS_FLESH.description": "Gain an additional {Energy:energyIcons()} at the start of your [blue]3rd[/blue] turn, and every turn after that.", "PAELS_FLESH.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PAELS_FLESH.title": "<PERSON><PERSON>'s Flesh", "PAELS_HORN.description": "Upon pickup, add [blue]2[/blue] [gold]Relax[/gold] to your [gold]Deck[/gold].", "PAELS_HORN.eventDescription": "Add [blue]2[/blue] [gold]Relax[/gold] to your [gold]Deck[/gold].", "PAELS_HORN.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PAELS_HORN.title": "<PERSON><PERSON>'s Horn", "PAELS_TEARS.description": "If you end your turn with unspent {energyPrefix:energyIcons(1)}, gain an additional {Energy:energyIcons()} next turn.", "PAELS_TEARS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PAELS_TEARS.title": "<PERSON><PERSON>'s Tears", "PAELS_WING.description": "When adding cards to your [gold]Deck[/gold], you may sacrifice the choice to <PERSON><PERSON> instead. Every {Sacrifices:plural:sacrifice|[blue]{}[/blue] sacrifices}, obtain a random [gold]Relic[/gold].", "PAELS_WING.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PAELS_WING.title": "Pa<PERSON>'s Wing", "PANDORAS_BOX.description": "[gold]Transform[/gold] ALL [gold]Strikes[/gold] and [gold]Defends[/gold].", "PANDORAS_BOX.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PANDORAS_BOX.infoText": "Cards transformed by [gold]Pandora's Box[/gold].", "PANDORAS_BOX.title": "Pandora's Box", "PANTOGRAPH.description": "At the start of each [gold]Boss[/gold] combat, heal [green]{Heal}[/green] HP.", "PANTOGRAPH.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PANTOGRAPH.title": "Pantograph", "PAPER_KRANE.description": "Enemies with [gold]Weak[/gold] deal [blue]40%[/blue] less damage to you rather than [blue]25%[/blue].", "PAPER_KRANE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PAPER_KRANE.title": "Paper Krane", "PAPER_PHROG.description": "Enemies with [gold]Vulnerable[/gold] take [blue]75%[/blue] more damage rather than [blue]50%[/blue].", "PAPER_PHROG.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PAPER_PHROG.title": "Paper Phrog", "PARRYING_SHIELD.description": "If you end a turn with at least [blue]{Block}[/blue] [gold]Block[/gold], deal [blue]{Damage}[/blue] damage to a random enemy.", "PARRYING_SHIELD.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PARRYING_SHIELD.title": "Parrying Shield", "PEAR.description": "Upon pickup, raise your Max HP by [blue]{MaxHp}[/blue].", "PEAR.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PEAR.title": "Pear", "PENDULUM.description": "Whenever you shuffle your [gold]Draw Pile[/gold], draw a card.", "PENDULUM.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PENDULUM.title": "Pen<PERSON><PERSON>", "PEN_NIB.description": "Every [blue]10th[/blue] [gold]Attack[/gold] you play deals double damage.", "PEN_NIB.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PEN_NIB.title": "<PERSON>", "PERPETUAL_COIN.description": "Upon pickup, gain [blue]{Gold}[/blue] [gold]Gold[/gold]. Merchants no longer appear in [gold]?[/gold] rooms.", "PERPETUAL_COIN.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PERPETUAL_COIN.title": "Perpetual Coin", "PETRIFIED_TOAD.description": "At the start of each combat, procure a [gold]Potion-Shaped Rock[/gold].", "PETRIFIED_TOAD.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PETRIFIED_TOAD.title": "Petrified Toad", "PHILOSOPHERS_STONE.description": "Gain {Energy:energyIcons()} at the start of your turn. ALL enemies start combat with [blue]{Strength}[/blue] [gold]Strength[/gold].", "PHILOSOPHERS_STONE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PHILOSOPHERS_STONE.title": "Philoso<PERSON>'s Stone", "PHYLACTERY_UNBOUND.description": "At the start of your turn, [gold]Summon[/gold] [blue]{<PERSON>mm<PERSON>}[/blue].", "PHYLACTERY_UNBOUND.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PHYLACTERY_UNBOUND.title": "Phylactery Unbound", "PITCH_BLACK_OIL.description": "Upon pickup, [gold]Upgrade[/gold] [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "PITCH_BLACK_OIL.eventDescription": "[gold]Upgrade[/gold] [blue]{Cards}[/blue] {Cards:plural:card|cards}.", "PITCH_BLACK_OIL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PITCH_BLACK_OIL.selectionScreenPrompt": "Select [blue]{Cards}[/blue] {Cards:plural:card|cards} to [gold]Upgrade[/gold]", "PITCH_BLACK_OIL.title": "Pitch Black Oil", "PLANISPHERE.description": "Whenever you enter a [gold]?[/gold] room, heal [green]{Heal}[/green] HP.", "PLANISPHERE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PLANISPHERE.title": "Planisphere", "POCKETWATCH.description": "Whenever you play [blue]{CardThreshold}[/blue] or less {CardThreshold:plural:card|cards} during your turn, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards} at the start of your next turn.", "POCKETWATCH.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "POCKETWATCH.title": "Pocketwatch", "POTION_BELT.description": "Upon pickup, gain [blue]{PotionSlots}[/blue] potion {PotionSlots:plural:slot|slots}.", "POTION_BELT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "POTION_BELT.title": "Potion Belt", "PRAYER_WHEEL.description": "Normal enemies drop an additional card reward.", "PRAYER_WHEEL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PRAYER_WHEEL.title": "Prayer Wheel", "PRISMATIC_SHARD.description": "Card rewards now contain cards from other colors.", "PRISMATIC_SHARD.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PRISMATIC_SHARD.title": "Prismatic Shard", "PULSING_CORE.description": "Every [blue]{Turns}[/blue] {Turns:plural:turn|turns}, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}.", "PULSING_CORE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PULSING_CORE.title": "Pulsing Core", "PUNCH_DAGGER.description": "[gold]Enchant[/gold] an [gold]Attack[/gold] with [purple]Momentum[/purple] [blue]{Momentum}[/blue].", "PUNCH_DAGGER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PUNCH_DAGGER.title": "<PERSON>", "PUNGENT_POULTICE.description": "Upon pickup, [gold]Transform[/gold] [blue]1[/blue] of your [gold]Strikes[/gold] and [blue]1[/blue] of your [gold]Defends[/gold] and lose [blue]{MaxHp}[/blue] Max HP.", "PUNGENT_POULTICE.eventDescription": "[gold]Transform[/gold] [blue]1[/blue] of your [gold]Strikes[/gold] and [blue]1[/blue] of your [gold]Defends[/gold]. [red]Lose {MaxHp} Max HP.[/red]", "PUNGENT_POULTICE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "PUNGENT_POULTICE.title": "Pungent Poultice", "RAINBOW_RING.description": "Whenever you play an [gold]Attack[/gold], [gold]Skill[/gold], and [gold]Power[/gold] in a single turn, gain [blue]{Strength}[/blue] [gold]Strength[/gold] and [blue]{Dexterity}[/blue] [gold]Dexterity[/gold].", "RAINBOW_RING.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RAINBOW_RING.title": "Rainbow Ring", "RAZOR_TOOTH.description": "Every time you play a card, [gold]Upgrade[/gold] it for the remainder of combat.", "RAZOR_TOOTH.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RAZOR_TOOTH.title": "<PERSON><PERSON>", "RECURSIVE_CORE.description": "Upon pickup, replace {StarterRelic.StringValue:cond:[gold]{StarterRelic}[/gold] with [gold]{UpgradedRelic}[/gold]|your starter relic with an ancient version}.", "RECURSIVE_CORE.eventDescription": "Replace {StarterRelic.StringValue:cond:[gold]{StarterRelic}[/gold] with [gold]{UpgradedRelic}[/gold]|your starter relic with an ancient version}.", "RECURSIVE_CORE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RECURSIVE_CORE.title": "Recursive Core", "RED_MASK.description": "At the start of each combat, apply [blue]{Weak}[/blue] [gold]Weak[/gold] to ALL enemies.", "RED_MASK.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RED_MASK.title": "Red Mask", "RED_SKULL.description": "While your HP is at or below [blue]{HpThreshold}%[/blue], you have [blue]{Strength}[/blue] additional [gold]Strength[/gold].", "RED_SKULL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RED_SKULL.title": "Red Skull", "RED_VINE_TEA.description": "At the start of the next {Combats:plural:combat|[blue]{}[/blue] combats}, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}.", "RED_VINE_TEA.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RED_VINE_TEA.title": "Red Vine Tea", "REGALITE.description": "The first time you play a Colorless card each turn, gain {Energy:energyIcons()}.", "REGALITE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "REGALITE.title": "Regalite", "REGAL_PILLOW.additionalRestSiteHealText": "+{<PERSON><PERSON>} HP from Regal Pillow.", "REGAL_PILLOW.description": "Whenever you [gold]Rest[/gold], heal an additional [blue]{Heal}[/blue] HP.", "REGAL_PILLOW.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "REGAL_PILLOW.title": "Regal Pillow", "REPLICATOR.description": "Upon pickup, [gold]Enchant[/gold] a card with [purple]Cloning Capsule[/purple].", "REPLICATOR.eventDescription": "[gold]Enchant[/gold] a card with [purple]Cloning Capsule[/purple].", "REPLICATOR.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "REPLICATOR.title": "Replicator", "REPTILE_TRINKET.description": "Whenever you use a potion, gain [blue]{Strength}[/blue] [gold]Strength[/gold] this turn.", "REPTILE_TRINKET.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "REPTILE_TRINKET.title": "Reptile Trinket", "RETRACTABLE_CLAWS.description": "Upon pickup, [gold]Transform[/gold] up to [blue]{Cards}[/blue] {Cards:plural:card|cards} into [gold]Maul[/gold].", "RETRACTABLE_CLAWS.eventDescription": "[gold]Transform[/gold] up to [blue]{Cards}[/blue] {Cards:plural:card|cards} into [gold]Maul[/gold].", "RETRACTABLE_CLAWS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RETRACTABLE_CLAWS.selectionScreenPrompt": "Select Cards to Transform into Maul.", "RETRACTABLE_CLAWS.title": "Retractable Claws", "RINGING_TRIANGLE.description": "[gold]Retain[/gold] your [gold]Hand[/gold] on the first turn of combat.", "RINGING_TRIANGLE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RINGING_TRIANGLE.title": "Ringing Triangle", "RING_OF_THE_DRAKE.description": "At the start of your first {Turns:plural:turn|[blue]{}[/blue] turns}, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}.", "RING_OF_THE_DRAKE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RING_OF_THE_DRAKE.title": "Ring of the Drake", "RING_OF_THE_SNAKE.description": "At the start of each combat, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}.", "RING_OF_THE_SNAKE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RING_OF_THE_SNAKE.title": "Ring of the Snake", "RIPPLE_BASIN.description": "If you did not play any [gold]Attacks[/gold] during your turn, gain [blue]{Block}[/blue] [gold]Block[/gold].", "RIPPLE_BASIN.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RIPPLE_BASIN.title": "Ripple Basin", "ROYAL_POISON.description": "At the start of each combat, lose [blue]{Damage}[/blue] HP.", "ROYAL_POISON.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ROYAL_POISON.title": "Royal Poison", "ROYAL_STAMP.description": "Upon pickup, choose an [gold]Attack[/gold] or [gold]Skill[/gold] in your [gold]Deck[/gold] to [gold]Enchant[/gold] with [purple]{Enchantment}[/purple].", "ROYAL_STAMP.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ROYAL_STAMP.title": "Royal Stamp", "RUBY_EARRINGS.description": "All card rewards contain one card of each Rarity.", "RUBY_EARRINGS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RUBY_EARRINGS.title": "<PERSON>", "RUNIC_CAPACITOR.description": "Start each combat with [blue]{Repeat}[/blue] additional [gold]Orb[/gold] {Repeat:plural:slot|slots}.", "RUNIC_CAPACITOR.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RUNIC_CAPACITOR.title": "Runic Capacitor", "RUNIC_PYRAMID.description": "At the end of your turn, you no longer discard your [gold]Hand[/gold].", "RUNIC_PYRAMID.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RUNIC_PYRAMID.title": "Runic Pyramid", "RUSTY_CHEST.description": "Upon pickup, obtain a random [gold]Relic[/gold].", "RUSTY_CHEST.eventDescription": "Obtain a random [gold]Relic[/gold].", "RUSTY_CHEST.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "RUSTY_CHEST.title": "<PERSON> Chest", "SAPPHIRE_SEED.description": "Gain {Energy:energyIcons()} at the start of the next [blue]{Combats}[/blue] combats.", "SAPPHIRE_SEED.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SAPPHIRE_SEED.title": "Sapphire Seed", "SCISSORS.description": "Upon pickup, remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold].", "SCISSORS.eventDescription": "Remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold].", "SCISSORS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SCISSORS.title": "Scissors", "SCREAMING_FLAGON.description": "If you end your turn with no cards in your [gold]Hand[/gold], gain [blue]{Strength}[/blue] [gold]Strength[/gold].", "SCREAMING_FLAGON.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SCREAMING_FLAGON.title": "Screaming <PERSON><PERSON>", "SEAL_OF_GOLD.description": "Upon pickup, gain [blue]{Gold}[/blue] [gold]Gold[/gold]. You can no longer obtain [gold]Gold[/gold].", "SEAL_OF_GOLD.eventDescription": "Gain [blue]{Gold}[/blue] [gold]Gold[/gold]. You can no longer obtain [gold]Gold[/gold].", "SEAL_OF_GOLD.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SEAL_OF_GOLD.title": "Seal of Gold", "SEEKING_TENTACLE.description": "You may reroll each card reward once.", "SEEKING_TENTACLE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SEEKING_TENTACLE.title": "Seeking Tentacle", "SEETHE.description": "The first time you gain [gold]Strength[/gold] each combat, double the amount gained.", "SEETHE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SEETHE.title": "Seethe", "SELF_FORMING_CLAY.description": "Whenever you lose HP in combat, gain [blue]{BlockNextTurn}[/blue] [gold]Block[/gold] next turn.", "SELF_FORMING_CLAY.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SELF_FORMING_CLAY.title": "Self-Forming Clay", "SEVERED_GILL.description": "At the start of each combat, add [blue]{Cards}[/blue] [gold]{Cards:plural:<PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON>}[/gold] to your [gold]Hand[/gold].", "SEVERED_GILL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SEVERED_GILL.title": "Severed <PERSON>", "SHINY_MONOCLE.description": "Upon pickup, choose [blue]1[/blue] of [blue]3[/blue] [gold]Rare Cards[/gold] to add to your [gold]Deck[/gold].", "SHINY_MONOCLE.eventDescription": "Choose [blue]1[/blue] of [blue]3[/blue] [gold]Rare Cards[/gold] to add to your [gold]Deck[/gold].", "SHINY_MONOCLE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SHINY_MONOCLE.title": "<PERSON><PERSON>", "SHOVEL.description": "You can now dig at [gold]Rest Sites[/gold] to obtain a random [gold]Relic[/gold].", "SHOVEL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SHOVEL.title": "<PERSON><PERSON><PERSON>", "SHURIKEN.description": "Every time you play [blue]{Cards}[/blue] [gold]Attacks[/gold] in a single turn, gain [blue]{Strength}[/blue] [gold]Strength[/gold].", "SHURIKEN.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SHURIKEN.title": "<PERSON><PERSON><PERSON>", "SILVER_CRUCIBLE.description": "The first {Cards:plural:card reward you see is|[blue]{}[/blue] card rewards you see are} [gold]Upgraded[/gold].", "SILVER_CRUCIBLE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SILVER_CRUCIBLE.title": "Silver Crucible", "SLING_OF_COURAGE.description": "Start each [gold]Elite[/gold] combat with [blue]{Strength}[/blue] [gold]Strength[/gold].", "SLING_OF_COURAGE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SLING_OF_COURAGE.title": "Sling of Courage", "SMOLDERING_WICK.description": "Upon pickup, add [blue]1[/blue] [gold]Brightest Flame[/gold] to your [gold]Deck[/gold].", "SMOLDERING_WICK.eventDescription": "Add [blue]1[/blue] [gold]Brightest Flame[/gold] to your [gold]Deck[/gold].", "SMOLDERING_WICK.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SMOLDERING_WICK.title": "Smoldering Wick", "SNECKO_EYE.description": "At the start of your turn, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}. Start each combat [red]Confused[/red].", "SNECKO_EYE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SNECKO_EYE.title": "Snecko Eye", "SNECKO_SKULL.description": "Whenever you apply [gold]Poison[/gold], apply an additional [blue]{Poison}[/blue] [gold]Poison[/gold].", "SNECKO_SKULL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SNECKO_SKULL.title": "Snecko Skull", "SOUP_RECIPE.description": "[gold]Poison[/gold] ticks up at the end of turn instead of ticking down.", "SOUP_RECIPE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SOUP_RECIPE.title": "Soup Recipe", "SOZU.description": "Gain {Energy:energyIcons()} at the start of each turn. You can no longer obtain potions.", "SOZU.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SOZU.title": "Sozu", "SPARKLING_ROUGE.description": "At the start of your [blue]3rd[/blue] turn, gain [blue]{Strength}[/blue] [gold]Strength[/gold] and [blue]{Dexter<PERSON>}[/blue] [gold]Dexterity[/gold].", "SPARKLING_ROUGE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SPARKLING_ROUGE.title": "Sparkling Rouge", "SPECIAL_SLEEVE.description": "Upon pickup, choose an [gold]Attack[/gold] in your [gold]Deck[/gold]. [gold]Enchant[/gold] it with [purple]Favored[/purple].", "SPECIAL_SLEEVE.eventDescription": "Choose an [gold]Attack[/gold] in your [gold]Deck[/gold]. [gold]Enchant[/gold] it with [purple]Favored[/purple].", "SPECIAL_SLEEVE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SPECIAL_SLEEVE.title": "Special Sleeve", "SPIRALED_LENS.description": "Upon pickup, remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold]. Lose [blue]{MaxHp}[/blue] Max HP.", "SPIRALED_LENS.eventDescription": "Remove [blue]{Cards}[/blue] {Cards:plural:card|cards} from your [gold]Deck[/gold]. [red]Lose {MaxHp} Max HP.[/red]", "SPIRALED_LENS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SPIRALED_LENS.title": "Spiraled Lens", "STARTER_FLARE.description": "At the start of each combat, deal [blue]{Damage}[/blue] damage to ALL enemies.", "STARTER_FLARE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "STARTER_FLARE.title": "Starter <PERSON><PERSON><PERSON>", "STONE_CALENDAR.description": "At the end of turn [blue]{DamageTurn}[/blue], deal [blue]{Damage}[/blue] damage to ALL enemies.", "STONE_CALENDAR.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "STONE_CALENDAR.title": "Stone Calendar", "STONE_CRACKER.description": "At the start of [gold]Boss[/gold] combats, [gold]Upgrade[/gold] [blue]{Cards}[/blue] random {Cards:plural:card|cards} in your [gold]Draw Pile[/gold] for the rest of combat.", "STONE_CRACKER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "STONE_CRACKER.title": "Stone Cracker", "STONE_HUMIDIFIER.additionalRestSiteHealText": "Raise your Max HP by {MaxHp}.", "STONE_HUMIDIFIER.description": "Whenever you [gold]Rest[/gold] at a [gold]Rest Site[/gold], raise your Max HP by [blue]{MaxHp}[/blue].", "STONE_HUMIDIFIER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "STONE_HUMIDIFIER.title": "Stone Humidifier", "STRAWBERRY.description": "Upon pickup, raise your Max HP by [blue]{MaxHp}[/blue].", "STRAWBERRY.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "STRAWBERRY.title": "<PERSON><PERSON>berry", "STRIKE_DUMMY.description": "Cards containing “Strike” deal [blue]{ExtraDamage}[/blue] additional damage.", "STRIKE_DUMMY.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "STRIKE_DUMMY.title": "Strike Dummy", "STURDY_BRACE.description": "Upon pickup, raise your Max HP by [blue]{MaxHp}[/blue].", "STURDY_BRACE.eventDescription": "Raise your Max HP by [blue]{MaxHp}[/blue].", "STURDY_BRACE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "STURDY_BRACE.title": "<PERSON><PERSON><PERSON>", "SWORD_OF_JADE.description": "Start each combat with [blue]{Strength}[/blue] [gold]Strength[/gold].", "SWORD_OF_JADE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SWORD_OF_JADE.title": "Sword of Jade", "SWORD_OF_STONE.description": "Transforms into a powerful [gold]Relic[/gold] after defeating [blue]{Elites}[/blue] [gold]{Elites:plural:Elite|Elites}[/gold].", "SWORD_OF_STONE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SWORD_OF_STONE.title": "Sword of Stone", "SYMBIOTIC_VIRUS.description": "At the start of each combat, [gold]Channel[/gold] [blue]{Dark}[/blue] [gold]Dark[/gold].", "SYMBIOTIC_VIRUS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "SYMBIOTIC_VIRUS.title": "Symbiotic Virus", "TANXS_MIGHT.description": "Whenever you play an [gold]Attack[/gold], gain [blue]{Strength}[/blue] [gold]Strength[/gold] this turn.", "TANXS_MIGHT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "TANXS_MIGHT.title": "<PERSON><PERSON>'s Might", "TANXS_WHISTLE.description": "Upon pickup, add [blue]1[/blue] [gold]Whistle[/gold] to your [gold]Deck[/gold].", "TANXS_WHISTLE.eventDescription": "Add [blue]1[/blue] [gold]Whistle[/gold] to your [gold]Deck[/gold].", "TANXS_WHISTLE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "TANXS_WHISTLE.title": "<PERSON><PERSON>'s Whistle", "TEA_OF_SHAME.description": "At the start of the next combat, shuffle [blue]{Da<PERSON>Count}[/blue] [gold]Dazed[/gold] into your [gold]Draw Pile[/gold].", "TEA_OF_SHAME.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "TEA_OF_SHAME.title": "Tea of Shame", "TEZCATARAS_CANDLE.description": "Gain {Energy:energyIcons()} at the start of each turn. Extinguishes at the start of [gold]Act[/gold] [blue]3[/blue].", "TEZCATARAS_CANDLE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "TEZCATARAS_CANDLE.title": "Tezcatara's Candle", "THE_ABACUS.description": "Whenever you shuffle your [gold]Draw Pile[/gold], gain [blue]{Block}[/blue] [gold]Block[/gold].", "THE_ABACUS.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "THE_ABACUS.title": "The Abacus", "THE_BOOT.description": "Whenever you would deal [blue]{DamageThreshold}[/blue] or less unblocked attack damage, increase it to [blue]{DamageMinimum}[/blue].", "THE_BOOT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "THE_BOOT.title": "The Boot", "THE_COURIER.description": "The merchant no longer runs out of cards, relics, or potions and his prices are reduced by [blue]{Discount}%[/blue].", "THE_COURIER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "THE_COURIER.title": "The Courier", "THE_THRONE.description": "At the start of each combat, gain {Stars:starIcons()}.", "THE_THRONE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "THE_THRONE.title": "The Throne", "TINGSHA.description": "Whenever you discard a card during your turn, deal [blue]{Damage}[/blue] damage to a random enemy for each card discarded.", "TINGSHA.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "TINGSHA.title": "<PERSON><PERSON><PERSON>", "TINY_MAILBOX.additionalRestSiteHealText": "Procure a random potion from Tiny Mailbox.", "TINY_MAILBOX.description": "Whenever you [gold]Rest[/gold], procure a random potion.", "TINY_MAILBOX.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "TINY_MAILBOX.title": "Tiny Mailbox", "TOOLBOX.description": "At the start of each combat, choose [blue]1[/blue] of [blue]{Cards}[/blue] random Colorless cards and add the chosen card into your [gold]Hand[/gold].", "TOOLBOX.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "TOOLBOX.title": "Toolbox", "TOXIC_EGG.description": "Whenever you add a [gold]Skill[/gold] into your [gold]Deck[/gold], [gold]Upgrade[/gold] it.", "TOXIC_EGG.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "TOXIC_EGG.title": "Toxic Egg", "TRANSLUCENT_FROND.description": "At the start of each combat, fill all empty potion slots with random potions.", "TRANSLUCENT_FROND.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "TRANSLUCENT_FROND.title": "<PERSON><PERSON><PERSON>", "TUNGSTEN_ROD.description": "Whenever you would lose HP, lose [blue]{HpLossReduction}[/blue] less.", "TUNGSTEN_ROD.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "TUNGSTEN_ROD.title": "<PERSON><PERSON><PERSON>", "TUNING_FORK.description": "Whenever an enemy dies, apply any [gold]Weak[/gold] or [gold]Vulnerable[/gold] they had to ALL enemies.", "TUNING_FORK.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "TUNING_FORK.title": "Tuning Fork", "TWISTED_FUNNEL.description": "At the start of each combat, apply [blue]{Poison}[/blue] [gold]Poison[/gold] to ALL enemies.", "TWISTED_FUNNEL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "TWISTED_FUNNEL.title": "Twisted Funnel", "UNCEASING_TOP.description": "Whenever you have no cards in [gold]Hand[/gold] during your turn, draw a card.", "UNCEASING_TOP.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "UNCEASING_TOP.title": "Unceasing Top", "UNDYING_SIGIL.description": "You cannot die from [gold]Doom[/gold].", "UNDYING_SIGIL.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "UNDYING_SIGIL.title": "Undying Sigil", "VAJRA.description": "Start each combat with [blue]{Strength}[/blue] [gold]Strength[/gold].", "VAJRA.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "VAJRA.title": "<PERSON><PERSON><PERSON>", "VAMBRACE.description": "The first time you gain [gold]Block[/gold] from a card each combat, double the amount gained.", "VAMBRACE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "VAMBRACE.title": "<PERSON><PERSON><PERSON><PERSON>", "VELVET_CHOKER.description": "Gain {Energy:energyIcons()} at the start of your turn. You cannot play more than [blue]{Cards}[/blue] {Cards:plural:card|cards} per turn.", "VELVET_CHOKER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "VELVET_CHOKER.title": "<PERSON>", "VENERABLE_TEA_SET.description": "Whenever you enter a Rest Site, start the next combat with {Energy:energyIcons()}.", "VENERABLE_TEA_SET.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "VENERABLE_TEA_SET.title": "Venerable Tea Set", "VIBRANT_HALO.description": "Upon pickup, [gold]Transform[/gold] {StarterCard.StringValue:cond:[gold]{StarterCard}[/gold] into [gold]{AncientCard}[/gold]|a starter card with an ancient version}.", "VIBRANT_HALO.eventDescription": "Replace {StarterCard.StringValue:cond:[gold]{StarterCard}[/gold] with [gold]{AncientCard}[/gold]|a starter card with an ancient version}.", "VIBRANT_HALO.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "VIBRANT_HALO.title": "<PERSON><PERSON><PERSON>", "VINE_BRACELET.description": "Upon pickup, raise your Max HP by [blue]{MaxHp}[/blue].", "VINE_BRACELET.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "VINE_BRACELET.title": "Vine Bracelet", "VITRUVIAN_MINION.description": "Cards containing \"Minion\" no longer [gold]Exhaust[/gold].", "VITRUVIAN_MINION.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "VITRUVIAN_MINION.title": "Vitruvian Minion", "WAR_EFFIGY.description": "At the start of [gold]Boss[/gold] and [gold]Elite[/gold] combats, gain {Energy:energyIcons()} and draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}.", "WAR_EFFIGY.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "WAR_EFFIGY.title": "War Effigy", "WAR_PAINT.description": "Upon pickup, [gold]Upgrade[/gold] [blue]{Cards}[/blue] random [gold]{Cards:plural:Skill|Skills}[/gold].", "WAR_PAINT.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "WAR_PAINT.title": "War Paint", "WAX_CHOKER.description": "Upon pickup, obtain [blue]{Relics}[/blue] [gold]Wax {Relics:plural:Relic|Relics}[/gold]. Every [blue]{Combats}[/blue] {Combats:plural:combat|combats}, your left-most [gold]Wax Relic[/gold] will melt away.", "WAX_CHOKER.eventDescription": "Obtain [blue]{Relics}[/blue] [gold]Wax {Relics:plural:Relic|Relics}[/gold]. Every [blue]{Combats}[/blue] {Combats:plural:combat|combats}, your left-most [gold]Wax Relic[/gold] will melt away.", "WAX_CHOKER.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "WAX_CHOKER.title": "Wax Choker", "WAX_CHOKER.waxRelicPrefix": "Wax {Title}", "WHETSTONE.description": "Upon pickup, [gold]Upgrade[/gold] [blue]{Cards}[/blue] random [gold]{Cards:plural:Attack|Attacks}[/gold].", "WHETSTONE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "WHETSTONE.title": "Whetstone", "WHITE_BEAST_STATUE.description": "Potions always appear in combat rewards.", "WHITE_BEAST_STATUE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "WHITE_BEAST_STATUE.title": "White Beast Statue", "WHITE_STAR.description": "[gold]Elites[/gold] drop an additional [gold]Rare[/gold] card reward.", "WHITE_STAR.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "WHITE_STAR.title": "White Star", "WONGOS_MYSTERY_TICKET.description": "Receive [blue]{Repeat}[/blue] random [gold]{Repeat:plural:Relic|Relics}[/gold] after [blue]{RemainingCombats}[/blue] {RemainingCombats:plural:combat|combats}.", "WONGOS_MYSTERY_TICKET.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "WONGOS_MYSTERY_TICKET.title": "<PERSON><PERSON>'s Mystery Ticket", "WONGO_CUSTOMER_APPRECIATION_BADGE.description": "Does nothing.", "WONGO_CUSTOMER_APPRECIATION_BADGE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "WONGO_CUSTOMER_APPRECIATION_BADGE.title": "Wongo Customer Appreciation Badge", "ZLATIRS_CAPE.description": "At the start of each turn, draw [blue]{Cards}[/blue] additional {Cards:plural:card|cards}. You may not draw cards during your turn.", "ZLATIRS_CAPE.flavor": "[red]Details for this relic will be revealed in the future...[/red]", "ZLATIRS_CAPE.title": "Zlatir's Cape"}